/* Root Variables */
:root {
    --primary-color: #2196F3;
    --primary-dark: #1976D2;
    --secondary-color: #FF9800;
    --success-color: #4CAF50;
    --error-color: #F44336;
    --warning-color: #FF9800;
    --background-color: #f5f5f5;
    --surface-color: #ffffff;
    --text-primary: #212121;
    --text-secondary: #757575;
    --border-color: #e0e0e0;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
    --border-radius: 8px;
    --sidebar-width: 280px;
    --topbar-height: 60px;
    --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Mobile Topbar */
.topbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--topbar-height);
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    padding: 0 16px;
    box-shadow: var(--shadow);
    z-index: 1000;
}

.menu-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    margin-right: 16px;
}

.topbar h1 {
    flex: 1;
    font-size: 18px;
    font-weight: 500;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--success-color);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: var(--surface-color);
    box-shadow: var(--shadow);
    z-index: 1002;
    transition: var(--transition);
    overflow-y: auto;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h2 {
    color: var(--primary-color);
    font-size: 20px;
    font-weight: 600;
}

.close-menu {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-item {
    display: block;
    padding: 12px 20px;
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: var(--background-color);
    color: var(--primary-color);
}

.nav-item.active {
    background: var(--background-color);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    padding: 20px;
    min-height: 100vh;
}

/* Sections */
.section {
    display: none;
}

.section.active {
    display: block;
}

.section h2 {
    margin-bottom: 20px;
    color: var(--text-primary);
    font-size: 24px;
    font-weight: 600;
}

/* Cards */
.card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    text-align: center;
    margin-right: 12px;
    margin-bottom: 12px;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    box-shadow: var(--shadow-hover);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #F57C00;
    box-shadow: var(--shadow-hover);
}

/* Status Messages */
.status-message {
    margin-top: 16px;
    padding: 12px;
    border-radius: var(--border-radius);
    display: none;
}

.status-message.success {
    background: #E8F5E8;
    color: var(--success-color);
    border: 1px solid var(--success-color);
    display: block;
}

.status-message.error {
    background: #FFEBEE;
    color: var(--error-color);
    border: 1px solid var(--error-color);
    display: block;
}

.status-message.info {
    background: #E3F2FD;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    display: block;
}

/* Location Info */
.location-info {
    margin-top: 16px;
    padding: 16px;
    background: var(--background-color);
    border-radius: var(--border-radius);
    display: none;
}

.map-container {
    width: 100%;
    height: 300px;
    margin-top: 16px;
    border-radius: var(--border-radius);
    background: var(--background-color);
    display: none;
}

/* QR Scanner */
.qr-controls {
    margin-bottom: 20px;
}

.qr-video-container {
    position: relative;
    margin-top: 16px;
    display: none;
}

.qr-video-container video {
    width: 100%;
    max-width: 400px;
    border-radius: var(--border-radius);
}

.qr-video-container canvas {
    display: none;
}

.qr-result {
    margin-top: 16px;
    padding: 12px;
    background: var(--background-color);
    border-radius: var(--border-radius);
    display: none;
}

/* Image Capture */
.image-controls {
    margin-bottom: 20px;
}

.image-preview {
    margin-top: 16px;
    text-align: center;
}

.image-preview img {
    max-width: 100%;
    max-height: 300px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-only {
        display: flex !important;
    }
    
    .desktop-hidden {
        display: block !important;
    }
    
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        margin-top: var(--topbar-height);
        padding: 16px;
    }
}

@media (min-width: 769px) {
    .mobile-only {
        display: none !important;
    }
    
    .desktop-hidden {
        display: none !important;
    }
}
