<?php
require_once 'vendor/autoload.php';

use Minishlink\WebPush\VAPID;

/**
 * Generate real VAPID keys using the web-push library
 */

try {
    // Generate VAPID keys
    $keys = VAPID::createVapidKeys();
    
    $vapidData = [
        'public_key' => $keys['publicKey'],
        'private_key' => $keys['privateKey'],
        'subject' => 'mailto:<EMAIL>',
        'generated_at' => date('Y-m-d H:i:s')
    ];
    
    // Save to file
    $keysJson = json_encode($vapidData, JSON_PRETTY_PRINT);
    file_put_contents('vapid-keys-real.json', $keysJson);
    
    // Display results
    header('Content-Type: application/json');
    echo $keysJson;
    
    echo "\n\n<!-- SUCCESS -->\n";
    echo "<!-- Real VAPID keys generated successfully! -->\n";
    echo "<!-- Update your configuration files with these keys -->\n";
    echo "<!-- Public Key: " . $keys['publicKey'] . " -->\n";
    echo "<!-- Private Key: " . $keys['privateKey'] . " -->\n";
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Failed to generate VAPID keys',
        'message' => $e->getMessage()
    ]);
}
?>
