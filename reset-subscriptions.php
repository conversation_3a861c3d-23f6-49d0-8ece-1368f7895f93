<?php
/**
 * Reset subscriptions after VAPID key change
 * This script clears all existing subscriptions that were created with old VAPID keys
 */

header('Content-Type: application/json');

$subscriptionDir = 'subscriptions/';
$backupDir = 'subscriptions/backup_' . date('Y-m-d_H-i-s') . '/';

try {
    // Create backup directory
    if (!is_dir($backupDir)) {
        mkdir($backupDir, 0755, true);
    }
    
    $cleared = 0;
    $backed_up = 0;
    
    // Get all subscription files
    $files = glob($subscriptionDir . 'subscription_*.json');
    
    foreach ($files as $file) {
        $filename = basename($file);
        
        // Backup the file
        if (copy($file, $backupDir . $filename)) {
            $backed_up++;
        }
        
        // Remove the original file
        if (unlink($file)) {
            $cleared++;
        }
    }
    
    // Also clear log files (optional)
    $logFiles = [
        $subscriptionDir . 'activity.log',
        $subscriptionDir . 'notifications.log',
        $subscriptionDir . 'location.log'
    ];
    
    $logs_cleared = 0;
    foreach ($logFiles as $logFile) {
        if (file_exists($logFile)) {
            if (copy($logFile, $backupDir . basename($logFile))) {
                if (unlink($logFile)) {
                    $logs_cleared++;
                }
            }
        }
    }
    
    // Log the reset action
    $resetLog = [
        'timestamp' => date('Y-m-d H:i:s'),
        'action' => 'SUBSCRIPTION_RESET',
        'reason' => 'VAPID key change',
        'subscriptions_cleared' => $cleared,
        'files_backed_up' => $backed_up,
        'logs_cleared' => $logs_cleared,
        'backup_location' => $backupDir,
        'new_vapid_public_key' => 'BMvi4N7x39F3rsHx2u26sGF9DxcFgl1tape7-2Xhmux2rTwMV28D8ZSrjbY5fn365TnSCqDP0eXl_Z2l_Y0dF_U'
    ];
    
    file_put_contents($subscriptionDir . 'reset.log', json_encode($resetLog) . "\n", FILE_APPEND | LOCK_EX);
    
    echo json_encode([
        'success' => true,
        'message' => 'Subscriptions reset successfully',
        'details' => [
            'subscriptions_cleared' => $cleared,
            'files_backed_up' => $backed_up,
            'logs_cleared' => $logs_cleared,
            'backup_location' => $backupDir,
            'next_steps' => [
                'Users need to re-subscribe to notifications',
                'Old subscriptions have been backed up',
                'New VAPID keys are now active'
            ]
        ]
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to reset subscriptions',
        'message' => $e->getMessage()
    ]);
}
?>
