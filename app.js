// Main application JavaScript

// Global variables
let swRegistration = null;
let isSubscribed = false;
let currentPosition = null;

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize the application
async function initializeApp() {
    console.log('Initializing PWA App...');
    
    // Register service worker
    if ('serviceWorker' in navigator) {
        try {
            swRegistration = await navigator.serviceWorker.register('/sw.js');
            console.log('Service Worker registered successfully');
            
            // Check if already subscribed
            checkSubscriptionStatus();
        } catch (error) {
            console.error('Service Worker registration failed:', error);
        }
    }
    
    // Initialize notification form
    const notificationForm = document.getElementById('notificationForm');
    if (notificationForm) {
        notificationForm.addEventListener('submit', handleNotificationSubmit);
    }
    
    // Update status indicator
    updateStatusIndicator();
}

// Navigation functions
function showSection(sectionId) {
    // Hide all sections
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => section.classList.remove('active'));
    
    // Show selected section
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // Update navigation
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => item.classList.remove('active'));
    event.target.classList.add('active');
    
    // Close mobile menu
    closeMobileMenu();
}

function toggleMobileMenu() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileMenuOverlay');
    
    sidebar.classList.toggle('active');
    overlay.classList.toggle('active');
}

function closeMobileMenu() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileMenuOverlay');
    
    sidebar.classList.remove('active');
    overlay.classList.remove('active');
}

// Push notification functions
async function subscribeToNotifications() {
    if (!swRegistration) {
        showStatus('subscriptionStatus', 'Service Worker not available', 'error');
        return;
    }
    
    try {
        // Request notification permission
        const permission = await Notification.requestPermission();
        if (permission !== 'granted') {
            showStatus('subscriptionStatus', 'Notification permission denied', 'error');
            return;
        }
        
        // Subscribe to push notifications
        const subscription = await swRegistration.pushManager.subscribe({
            userVisibleOnly: true,
            applicationServerKey: urlBase64ToUint8Array(getVapidPublicKey())
        });
        
        // Send subscription to server
        const response = await fetch('/php/subscribe.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(subscription)
        });
        
        if (response.ok) {
            isSubscribed = true;
            showStatus('subscriptionStatus', 'Successfully subscribed to notifications!', 'success');
            updateSubscribeButton();
            updateStatusIndicator();
        } else {
            throw new Error('Failed to save subscription');
        }
        
    } catch (error) {
        console.error('Subscription failed:', error);
        showStatus('subscriptionStatus', 'Subscription failed: ' + error.message, 'error');
    }
}

async function checkSubscriptionStatus() {
    if (!swRegistration) return;
    
    try {
        const subscription = await swRegistration.pushManager.getSubscription();
        isSubscribed = subscription !== null;
        updateSubscribeButton();
        updateStatusIndicator();
    } catch (error) {
        console.error('Error checking subscription:', error);
    }
}

function updateSubscribeButton() {
    const subscribeBtn = document.getElementById('subscribeBtn');
    if (subscribeBtn) {
        if (isSubscribed) {
            subscribeBtn.textContent = 'Subscribed ✓';
            subscribeBtn.disabled = true;
            subscribeBtn.classList.add('btn-success');
        } else {
            subscribeBtn.textContent = 'Subscribe to Notifications';
            subscribeBtn.disabled = false;
            subscribeBtn.classList.remove('btn-success');
        }
    }
}

async function sendTestNotification() {
    try {
        const response = await fetch('/php/send-notification.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                title: 'Test Notification',
                body: 'This is a test notification from your PWA app!',
                icon: '/icons/icon-192x192.png'
            })
        });
        
        if (response.ok) {
            showStatus('testStatus', 'Test notification sent successfully!', 'success');
        } else {
            throw new Error('Failed to send notification');
        }
    } catch (error) {
        console.error('Test notification failed:', error);
        showStatus('testStatus', 'Failed to send test notification: ' + error.message, 'error');
    }
}

async function handleNotificationSubmit(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const notificationData = {
        title: formData.get('title'),
        body: formData.get('body'),
        icon: formData.get('icon') || '/icons/icon-192x192.png'
    };
    
    try {
        const response = await fetch('/php/send-notification.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(notificationData)
        });
        
        if (response.ok) {
            showStatus('sendStatus', 'Notification sent successfully!', 'success');
            event.target.reset();
        } else {
            throw new Error('Failed to send notification');
        }
    } catch (error) {
        console.error('Send notification failed:', error);
        showStatus('sendStatus', 'Failed to send notification: ' + error.message, 'error');
    }
}

// Location functions
async function getCurrentLocation() {
    if (!navigator.geolocation) {
        showStatus('locationStatus', 'Geolocation is not supported by this browser', 'error');
        return;
    }
    
    showStatus('locationStatus', 'Getting your location...', 'info');
    
    try {
        const position = await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            });
        });
        
        currentPosition = position;
        await displayLocationInfo(position);
        await reverseGeocode(position.coords.latitude, position.coords.longitude);
        
    } catch (error) {
        console.error('Location error:', error);
        showStatus('locationStatus', 'Failed to get location: ' + error.message, 'error');
    }
}

async function displayLocationInfo(position) {
    const locationInfo = document.getElementById('locationInfo');
    const mapContainer = document.getElementById('map');
    
    const coords = position.coords;
    const accuracy = Math.round(coords.accuracy);
    
    locationInfo.innerHTML = `
        <h4>Location Information</h4>
        <p><strong>Latitude:</strong> ${coords.latitude.toFixed(6)}</p>
        <p><strong>Longitude:</strong> ${coords.longitude.toFixed(6)}</p>
        <p><strong>Accuracy:</strong> ${accuracy} meters</p>
        <p><strong>Timestamp:</strong> ${new Date(position.timestamp).toLocaleString()}</p>
    `;
    
    locationInfo.style.display = 'block';
    
    // Simple map placeholder (you can integrate with Google Maps, OpenStreetMap, etc.)
    mapContainer.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #e0e0e0; color: #666;">
            <div style="text-align: center;">
                <p>📍 Map View</p>
                <p>Lat: ${coords.latitude.toFixed(4)}, Lng: ${coords.longitude.toFixed(4)}</p>
                <p><small>Integrate with your preferred map service</small></p>
            </div>
        </div>
    `;
    mapContainer.style.display = 'block';
    
    showStatus('locationStatus', 'Location retrieved successfully!', 'success');
}

async function reverseGeocode(lat, lng) {
    // This is a placeholder for reverse geocoding
    // You can integrate with services like Google Maps Geocoding API, OpenStreetMap Nominatim, etc.
    console.log(`Reverse geocoding for ${lat}, ${lng}`);
    
    // For demo purposes, we'll just show the coordinates
    // In a real app, you would make an API call to get the address
}

// Utility functions
function showStatus(elementId, message, type) {
    const statusElement = document.getElementById(elementId);
    if (statusElement) {
        statusElement.textContent = message;
        statusElement.className = `status-message ${type}`;
        statusElement.style.display = 'block';
        
        // Auto-hide after 5 seconds for success messages
        if (type === 'success') {
            setTimeout(() => {
                statusElement.style.display = 'none';
            }, 5000);
        }
    }
}

function updateStatusIndicator() {
    const indicator = document.getElementById('statusIndicator');
    if (indicator) {
        if (isSubscribed) {
            indicator.style.backgroundColor = 'var(--success-color)';
            indicator.title = 'Subscribed to notifications';
        } else {
            indicator.style.backgroundColor = 'var(--warning-color)';
            indicator.title = 'Not subscribed to notifications';
        }
    }
}

function urlBase64ToUint8Array(base64String) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
        .replace(/-/g, '+')
        .replace(/_/g, '/');
    
    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);
    
    for (let i = 0; i < rawData.length; ++i) {
        outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
}

function getVapidPublicKey() {
    // VAPID public key generated from generate-real-vapid-keys.php
    return 'BG6ohog9yRpoL4JZfnLlCUj8KyOJDEnZqcDmabPpIxQaO0lIzZ5yrzKBl3rFiTVlOrvJynZOaVLhFUuIyFSUn_o';
}

// Image capture functions
function captureFromCamera() {
    const imageInput = document.getElementById('imageInput');
    imageInput.setAttribute('capture', 'camera');
    imageInput.click();
}

function selectFromGallery() {
    const imageInput = document.getElementById('imageInput');
    imageInput.removeAttribute('capture');
    imageInput.click();
}

function handleImageSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
        alert('Image size should be less than 5MB');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        displayImagePreview(e.target.result, file);
    };
    reader.readAsDataURL(file);
}

function displayImagePreview(dataUrl, file) {
    const imagePreview = document.getElementById('imagePreview');
    const imageDataField = document.getElementById('imageDataField');

    // Create image element
    const img = document.createElement('img');
    img.src = dataUrl;
    img.alt = 'Selected image';

    // Create info div
    const info = document.createElement('div');
    info.innerHTML = `
        <p><strong>File:</strong> ${file.name}</p>
        <p><strong>Size:</strong> ${(file.size / 1024).toFixed(2)} KB</p>
        <p><strong>Type:</strong> ${file.type}</p>
        <p><strong>Last Modified:</strong> ${new Date(file.lastModified).toLocaleString()}</p>
    `;
    info.style.marginTop = '10px';
    info.style.fontSize = '14px';
    info.style.color = 'var(--text-secondary)';

    // Clear previous content and add new
    imagePreview.innerHTML = '';
    imagePreview.appendChild(img);
    imagePreview.appendChild(info);

    // Set data field (first 100 characters of base64)
    imageDataField.value = dataUrl.substring(0, 100) + '...';
}
