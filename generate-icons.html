<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate PWA Icons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .icon-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        canvas {
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PWA Icon Generator</h1>
        <p>This tool generates placeholder icons for your PWA. Click "Generate Icons" to create all required sizes.</p>
        
        <button onclick="generateAllIcons()">Generate All Icons</button>
        <button onclick="downloadAllIcons()">Download All Icons</button>
        
        <div class="icon-grid" id="iconGrid">
            <!-- Icons will be generated here -->
        </div>
    </div>

    <script>
        const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];
        const canvases = {};

        function generateAllIcons() {
            const iconGrid = document.getElementById('iconGrid');
            iconGrid.innerHTML = '';

            iconSizes.forEach(size => {
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';

                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                canvas.style.width = '100px';
                canvas.style.height = '100px';

                const ctx = canvas.getContext('2d');
                
                // Create gradient background
                const gradient = ctx.createLinearGradient(0, 0, size, size);
                gradient.addColorStop(0, '#2196F3');
                gradient.addColorStop(1, '#1976D2');
                
                // Fill background
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, size, size);
                
                // Add rounded corners
                ctx.globalCompositeOperation = 'destination-in';
                ctx.beginPath();
                ctx.roundRect(0, 0, size, size, size * 0.1);
                ctx.fill();
                ctx.globalCompositeOperation = 'source-over';
                
                // Add icon symbol (notification bell)
                ctx.fillStyle = 'white';
                ctx.font = `${size * 0.4}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('🔔', size / 2, size / 2);

                const label = document.createElement('p');
                label.textContent = `${size}x${size}`;

                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = 'Download';
                downloadBtn.onclick = () => downloadIcon(canvas, size);

                iconItem.appendChild(canvas);
                iconItem.appendChild(label);
                iconItem.appendChild(downloadBtn);
                iconGrid.appendChild(iconItem);

                canvases[size] = canvas;
            });
        }

        function downloadIcon(canvas, size) {
            const link = document.createElement('a');
            link.download = `icon-${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }

        function downloadAllIcons() {
            iconSizes.forEach(size => {
                if (canvases[size]) {
                    setTimeout(() => downloadIcon(canvases[size], size), size * 10);
                }
            });
        }

        // Add roundRect polyfill for older browsers
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }

        // Generate icons on page load
        window.onload = generateAllIcons;
    </script>
</body>
</html>
