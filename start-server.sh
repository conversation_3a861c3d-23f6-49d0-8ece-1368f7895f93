#!/bin/bash

# PWA App Local Development Server
# This script starts a local PHP development server

echo "🚀 Starting PWA App Development Server..."
echo "📁 Current directory: $(pwd)"
echo "🔧 PHP Version: $(php -v | head -n 1)"

# Check if PHP is installed
if ! command -v php &> /dev/null; then
    echo "❌ PHP is not installed or not in PATH"
    echo "Please install PHP to run this server"
    exit 1
fi

# Create subscriptions directory if it doesn't exist
if [ ! -d "subscriptions" ]; then
    echo "📁 Creating subscriptions directory..."
    mkdir -p subscriptions
    chmod 755 subscriptions
fi

# Set proper permissions
echo "🔐 Setting permissions..."
chmod 755 subscriptions
chmod 644 *.html *.css *.js *.json 2>/dev/null || true
chmod 644 php/*.php 2>/dev/null || true

# Default port
PORT=${1:-8000}

echo "🌐 Starting PHP development server on port $PORT..."
echo "📱 Open your browser and go to:"
echo "   http://localhost:$PORT"
echo ""
echo "🔔 For PWA features to work properly, you may need HTTPS."
echo "   Consider using ngrok or a similar tool for HTTPS in development."
echo ""
echo "⚡ Server is running... Press Ctrl+C to stop"
echo ""

# Start the PHP development server
php -S localhost:$PORT -t .

echo ""
echo "👋 Server stopped. Goodbye!"
