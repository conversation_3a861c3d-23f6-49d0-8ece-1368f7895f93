<?php
/**
 * Simple icon generator for PWA
 * Creates basic colored squares as placeholder icons
 */

$iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];
$iconDir = 'icons/';

// Create icons directory if it doesn't exist
if (!is_dir($iconDir)) {
    mkdir($iconDir, 0755, true);
}

foreach ($iconSizes as $size) {
    $filename = $iconDir . "icon-{$size}x{$size}.png";
    
    // Create image
    $image = imagecreate($size, $size);
    
    // Define colors
    $blue = imagecolorallocate($image, 33, 150, 243); // #2196F3
    $white = imagecolorallocate($image, 255, 255, 255);
    
    // Fill background
    imagefill($image, 0, 0, $blue);
    
    // Add notification bell icon (simplified)
    $centerX = $size / 2;
    $centerY = $size / 2;
    $bellSize = $size * 0.4;
    
    // Draw bell shape (simplified rectangle with rounded top)
    $bellLeft = $centerX - $bellSize / 2;
    $bellRight = $centerX + $bellSize / 2;
    $bellTop = $centerY - $bellSize / 2;
    $bellBottom = $centerY + $bellSize / 4;
    
    // Bell body
    imagefilledrectangle($image, $bellLeft, $bellTop + $bellSize/4, $bellRight, $bellBottom, $white);
    
    // Bell top (rounded)
    imagefilledellipse($image, $centerX, $bellTop + $bellSize/4, $bellSize, $bellSize/2, $white);
    
    // Bell clapper
    imagefilledellipse($image, $centerX, $bellBottom + $bellSize/8, $bellSize/4, $bellSize/4, $white);
    
    // Add notification dot
    $dotSize = $size * 0.15;
    imagefilledellipse($image, $centerX + $bellSize/3, $centerY - $bellSize/3, $dotSize, $dotSize, imagecolorallocate($image, 255, 68, 68));
    
    // Save image
    imagepng($image, $filename);
    imagedestroy($image);
    
    echo "Created: $filename\n";
}

echo "All icons created successfully!\n";
?>
