# PWA App .htaccess Configuration

# Enable rewrite engine
RewriteEngine On

# MIME Types for PWA files
AddType application/manifest+json .webmanifest
AddType application/manifest+json .json
AddType text/cache-manifest .appcache

# Service Worker MIME type
<Files "sw.js">
    Header set Content-Type "application/javascript"
    Header set Service-Worker-Allowed "/"
</Files>

# Manifest MIME type
<Files "manifest.json">
    Header set Content-Type "application/manifest+json"
</Files>

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# HTTPS Redirect (uncomment for production)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Cache Control for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # Manifest and Service Worker (short cache)
    ExpiresByType application/manifest+json "access plus 1 day"
    
    # HTML (no cache)
    ExpiresByType text/html "access plus 0 seconds"
</IfModule>

# Gzip Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/manifest+json
</IfModule>

# Protect sensitive files
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<FilesMatch "\.(json)$">
    <If "%{REQUEST_URI} =~ m#^/subscriptions/#">
        Order allow,deny
        Deny from all
    </If>
</FilesMatch>

# Allow access to PHP files in php directory
<Directory "php">
    Order allow,deny
    Allow from all
</Directory>

# Prevent access to subscription files from web
<Directory "subscriptions">
    Order allow,deny
    Deny from all
</Directory>

# Error pages (optional)
ErrorDocument 404 /index.html
ErrorDocument 403 /index.html
