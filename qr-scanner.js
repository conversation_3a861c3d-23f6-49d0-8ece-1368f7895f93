// QR Code Scanner functionality

let qrScanner = null;
let qrVideoElement = null;
let qrCanvas = null;
let qrContext = null;
let isScanning = false;

// Initialize QR scanner when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    qrVideoElement = document.getElementById('qrVideoElement');
    qrCanvas = document.getElementById('qrCanvas');
    if (qrCanvas) {
        qrContext = qrCanvas.getContext('2d');
    }
});

// Start QR code scanning
async function startQRScanner() {
    if (isScanning) {
        stopQRScanner();
        return;
    }
    
    try {
        // Check if camera is available
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            throw new Error('Camera not supported by this browser');
        }
        
        // Request camera permission
        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'environment', // Use back camera if available
                width: { ideal: 640 },
                height: { ideal: 480 }
            }
        });
        
        qrVideoElement.srcObject = stream;
        qrVideoElement.play();
        
        // Show video container
        const qrVideoContainer = document.getElementById('qrVideo');
        qrVideoContainer.style.display = 'block';
        
        // Update button
        const startScanBtn = document.getElementById('startScanBtn');
        startScanBtn.textContent = 'Stop Scanner';
        startScanBtn.classList.add('btn-secondary');
        startScanBtn.classList.remove('btn-primary');
        
        isScanning = true;
        
        // Start scanning loop
        requestAnimationFrame(scanQRCode);
        
        console.log('QR Scanner started');
        
    } catch (error) {
        console.error('QR Scanner error:', error);
        showQRError('Failed to start camera: ' + error.message);
    }
}

// Stop QR code scanning
function stopQRScanner() {
    if (qrVideoElement && qrVideoElement.srcObject) {
        const tracks = qrVideoElement.srcObject.getTracks();
        tracks.forEach(track => track.stop());
        qrVideoElement.srcObject = null;
    }
    
    // Hide video container
    const qrVideoContainer = document.getElementById('qrVideo');
    qrVideoContainer.style.display = 'none';
    
    // Update button
    const startScanBtn = document.getElementById('startScanBtn');
    startScanBtn.textContent = 'Start QR Scanner';
    startScanBtn.classList.add('btn-primary');
    startScanBtn.classList.remove('btn-secondary');
    
    isScanning = false;
    
    console.log('QR Scanner stopped');
}

// Scan for QR codes in video frame
function scanQRCode() {
    if (!isScanning || !qrVideoElement || qrVideoElement.readyState !== qrVideoElement.HAVE_ENOUGH_DATA) {
        if (isScanning) {
            requestAnimationFrame(scanQRCode);
        }
        return;
    }
    
    // Set canvas size to match video
    qrCanvas.width = qrVideoElement.videoWidth;
    qrCanvas.height = qrVideoElement.videoHeight;
    
    // Draw video frame to canvas
    qrContext.drawImage(qrVideoElement, 0, 0, qrCanvas.width, qrCanvas.height);
    
    // Get image data
    const imageData = qrContext.getImageData(0, 0, qrCanvas.width, qrCanvas.height);
    
    // Try to decode QR code
    try {
        const qrCode = jsQR(imageData.data, imageData.width, imageData.height);
        
        if (qrCode) {
            handleQRCodeDetected(qrCode.data);
            return; // Stop scanning after successful detection
        }
    } catch (error) {
        console.error('QR decode error:', error);
    }
    
    // Continue scanning
    requestAnimationFrame(scanQRCode);
}

// Handle detected QR code
function handleQRCodeDetected(data) {
    console.log('QR Code detected:', data);
    
    // Stop scanner
    stopQRScanner();
    
    // Display result
    const qrResult = document.getElementById('qrResult');
    const qrDataField = document.getElementById('qrDataField');
    
    qrResult.innerHTML = `
        <h4>QR Code Detected!</h4>
        <p><strong>Data:</strong> ${data}</p>
        <p><strong>Type:</strong> ${detectQRType(data)}</p>
        <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
    `;
    qrResult.style.display = 'block';
    
    // Set form field
    qrDataField.value = data;
    
    // Show success message
    showQRSuccess('QR Code scanned successfully!');
}

// Detect QR code type
function detectQRType(data) {
    if (data.startsWith('http://') || data.startsWith('https://')) {
        return 'URL';
    } else if (data.startsWith('mailto:')) {
        return 'Email';
    } else if (data.startsWith('tel:')) {
        return 'Phone';
    } else if (data.startsWith('sms:')) {
        return 'SMS';
    } else if (data.startsWith('wifi:')) {
        return 'WiFi';
    } else if (data.includes('@') && data.includes('.')) {
        return 'Email Address';
    } else if (/^\d+$/.test(data)) {
        return 'Number';
    } else {
        return 'Text';
    }
}

// Show QR success message
function showQRSuccess(message) {
    const qrResult = document.getElementById('qrResult');
    const successDiv = document.createElement('div');
    successDiv.className = 'status-message success';
    successDiv.textContent = message;
    successDiv.style.marginTop = '10px';
    
    qrResult.appendChild(successDiv);
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.parentNode.removeChild(successDiv);
        }
    }, 3000);
}

// Show QR error message
function showQRError(message) {
    const qrResult = document.getElementById('qrResult');
    qrResult.innerHTML = `
        <div class="status-message error">
            <strong>Error:</strong> ${message}
        </div>
    `;
    qrResult.style.display = 'block';
}

// Simple QR code decoder (jsQR alternative for basic functionality)
// This is a simplified version - in production, use a proper QR library like jsQR
function jsQR(data, width, height) {
    // This is a placeholder function
    // In a real implementation, you would use a proper QR code library
    // For now, we'll return null to indicate no QR code found
    
    // You can include the jsQR library by adding this script tag to your HTML:
    // <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
    
    if (typeof window.jsQR === 'function') {
        return window.jsQR(data, width, height);
    }
    
    return null;
}

// Manual QR data input (fallback)
function manualQRInput() {
    const data = prompt('Enter QR code data manually:');
    if (data && data.trim()) {
        handleQRCodeDetected(data.trim());
    }
}

// Clear QR result
function clearQRResult() {
    const qrResult = document.getElementById('qrResult');
    const qrDataField = document.getElementById('qrDataField');
    
    qrResult.style.display = 'none';
    qrResult.innerHTML = '';
    qrDataField.value = '';
}
