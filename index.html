<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Notification App</title>
    <link rel="stylesheet" href="style.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#2196F3">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="PWA App">
</head>
<body>
    <!-- Mobile Topbar -->
    <div class="topbar mobile-only">
        <button class="menu-toggle" onclick="toggleMobileMenu()">☰</button>
        <h1>PWA App</h1>
        <div class="status-indicator" id="statusIndicator"></div>
    </div>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-menu-overlay" id="mobileMenuOverlay" onclick="closeMobileMenu()"></div>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2>PWA App</h2>
            <button class="close-menu desktop-hidden" onclick="closeMobileMenu()">×</button>
        </div>
        <nav class="sidebar-nav">
            <a href="#" onclick="showSection('subscribe', this)" class="nav-item active">
                📱 Subscribe to App
            </a>
            <a href="#" onclick="showSection('test-notification', this)" class="nav-item">
                🔔 Test Push Notification
            </a>
            <a href="#" onclick="showSection('send-notification', this)" class="nav-item">
                📤 Send Push Notification
            </a>
            <a href="#" onclick="showSection('location', this)" class="nav-item">
                📍 Find My Location
            </a>
            <a href="#" onclick="showSection('qr-scanner', this)" class="nav-item">
                📷 QR Code Scanner
            </a>
            <a href="#" onclick="showSection('image-capture', this)" class="nav-item">
                🖼️ Image Capture
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- System Notification Banner -->
        <div class="system-banner" id="systemBanner" style="display: none;">
            <div class="banner-content">
                <span class="banner-icon">🔔</span>
                <span class="banner-text">Push notification system has been updated. Please re-subscribe to continue receiving notifications.</span>
                <button class="banner-close" onclick="closeBanner()">×</button>
            </div>
        </div>

        <!-- Subscribe Section -->
        <div class="section active" id="subscribe">
            <h2>Subscribe to Notifications</h2>
            <div class="card">
                <p>Enable push notifications to receive updates from this app.</p>
                <button id="subscribeBtn" class="btn btn-primary" onclick="subscribeToNotifications()">
                    Subscribe to Notifications
                </button>
                <div id="subscriptionStatus" class="status-message"></div>
            </div>
        </div>

        <!-- Test Notification Section -->
        <div class="section" id="test-notification">
            <h2>Test Push Notification</h2>
            <div class="card">
                <p>Send a test notification to your device.</p>
                <button id="testNotificationBtn" class="btn btn-secondary" onclick="sendTestNotification()">
                    Send Test Notification
                </button>
                <div id="testStatus" class="status-message"></div>
            </div>
        </div>

        <!-- Send Notification Section -->
        <div class="section" id="send-notification">
            <h2>Send Push Notification</h2>
            <div class="card">
                <form id="notificationForm">
                    <div class="form-group">
                        <label for="notificationTitle">Title:</label>
                        <input type="text" id="notificationTitle" name="title" required>
                    </div>
                    <div class="form-group">
                        <label for="notificationBody">Message:</label>
                        <textarea id="notificationBody" name="body" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="notificationIcon">Icon URL (optional):</label>
                        <input type="url" id="notificationIcon" name="icon" value="">
                    </div>
                    <button type="submit" class="btn btn-primary">Send Notification</button>
                </form>
                <div id="sendStatus" class="status-message"></div>
            </div>
        </div>

        <!-- Location Section -->
        <div class="section" id="location">
            <h2>Find My Location</h2>
            <div class="card">
                <button id="getLocationBtn" class="btn btn-primary" onclick="getCurrentLocation()">
                    Get Current Location
                </button>
                <div id="locationStatus" class="status-message"></div>
                <div id="locationInfo" class="location-info"></div>
                <div id="map" class="map-container"></div>
            </div>
        </div>

        <!-- QR Scanner Section -->
        <div class="section" id="qr-scanner">
            <h2>QR Code Scanner</h2>
            <div class="card">
                <div class="qr-controls">
                    <button id="startScanBtn" class="btn btn-primary" onclick="startQRScanner()">
                        Start QR Scanner
                    </button>
                    <button class="btn btn-secondary" onclick="manualQRInput()">
                        Manual Input
                    </button>
                    <button class="btn btn-secondary" onclick="clearQRResult()">
                        Clear Result
                    </button>
                </div>
                <div id="qrVideo" class="qr-video-container">
                    <video id="qrVideoElement" autoplay></video>
                    <canvas id="qrCanvas"></canvas>
                </div>
                <div id="qrResult" class="qr-result"></div>
                <div class="form-group">
                    <label for="qrDataField">Scanned Data:</label>
                    <input type="text" id="qrDataField" readonly>
                </div>
            </div>
        </div>

        <!-- Image Capture Section -->
        <div class="section" id="image-capture">
            <h2>Image Capture</h2>
            <div class="card">
                <div class="image-controls">
                    <button class="btn btn-primary" onclick="captureFromCamera()">
                        📷 Take Photo
                    </button>
                    <button class="btn btn-secondary" onclick="selectFromGallery()">
                        🖼️ Select from Gallery
                    </button>
                </div>
                <input type="file" id="imageInput" accept="image/*" style="display: none;" onchange="handleImageSelect(event)">
                <div id="imagePreview" class="image-preview"></div>
                <div class="form-group">
                    <label for="imageDataField">Image Data:</label>
                    <textarea id="imageDataField" readonly rows="3"></textarea>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
    <script src="app.js"></script>
    <script src="qr-scanner.js"></script>
</body>
</html>
