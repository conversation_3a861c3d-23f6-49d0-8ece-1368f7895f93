<?php
// Simple test file to verify PHP functionality

header('Content-Type: application/json');

$tests = [];

// Test 1: PHP Version
$tests['php_version'] = [
    'test' => 'PHP Version',
    'result' => phpversion(),
    'status' => version_compare(phpversion(), '7.0.0', '>=') ? 'PASS' : 'FAIL'
];

// Test 2: JSON Functions
$tests['json_support'] = [
    'test' => 'JSON Support',
    'result' => function_exists('json_encode') && function_exists('json_decode'),
    'status' => function_exists('json_encode') && function_exists('json_decode') ? 'PASS' : 'FAIL'
];

// Test 3: File Write Permissions
$testFile = 'subscriptions/test_write.txt';
$writeTest = @file_put_contents($testFile, 'test');
$tests['file_write'] = [
    'test' => 'File Write Permissions',
    'result' => $writeTest !== false,
    'status' => $writeTest !== false ? 'PASS' : 'FAIL'
];

// Clean up test file
if (file_exists($testFile)) {
    @unlink($testFile);
}

// Test 4: Subscriptions Directory
$tests['subscriptions_dir'] = [
    'test' => 'Subscriptions Directory',
    'result' => is_dir('subscriptions') && is_writable('subscriptions'),
    'status' => is_dir('subscriptions') && is_writable('subscriptions') ? 'PASS' : 'FAIL'
];

// Test 5: Required Extensions
$requiredExtensions = ['curl', 'openssl'];
$extensionResults = [];
foreach ($requiredExtensions as $ext) {
    $extensionResults[$ext] = extension_loaded($ext);
}
$tests['extensions'] = [
    'test' => 'Required Extensions',
    'result' => $extensionResults,
    'status' => array_reduce($extensionResults, function($carry, $item) {
        return $carry && $item;
    }, true) ? 'PASS' : 'FAIL'
];

// Test 6: HTTP Headers
$tests['headers'] = [
    'test' => 'HTTP Headers',
    'result' => function_exists('header'),
    'status' => function_exists('header') ? 'PASS' : 'FAIL'
];

// Overall status
$overallStatus = 'PASS';
foreach ($tests as $test) {
    if ($test['status'] === 'FAIL') {
        $overallStatus = 'FAIL';
        break;
    }
}

// Response
$response = [
    'timestamp' => date('Y-m-d H:i:s'),
    'overall_status' => $overallStatus,
    'tests' => $tests,
    'server_info' => [
        'php_version' => phpversion(),
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'Unknown'
    ]
];

echo json_encode($response, JSON_PRETTY_PRINT);
?>
