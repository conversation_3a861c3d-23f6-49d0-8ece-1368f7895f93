# PWA Notification App

A Progressive Web App with push notifications, location services, QR code scanning, and image capture functionality.

## Features

- 📱 **Installable PWA** - Can be installed on mobile and desktop devices
- 🔔 **Push Notifications** - Subscribe and receive push notifications
- 📍 **Location Services** - Get current location and reverse geocoding
- 📷 **QR Code Scanner** - Scan QR codes using device camera
- 🖼️ **Image Capture** - Take photos or select from gallery
- 📱 **Responsive Design** - Mobile topbar and desktop sidebar
- 💾 **Simple PHP Backend** - JSON file-based storage

## Installation

1. **Clone or download** this repository to your web server
2. **Ensure PHP is enabled** on your web server
3. **Set proper permissions** for the `subscriptions/` folder:
   ```bash
   chmod 755 subscriptions/
   ```
4. **Access the app** through your web browser
5. **Install the PWA** using your browser's install prompt

## File Structure

```
pwa1/
├── index.html              # Main app interface
├── manifest.json           # PWA manifest
├── sw.js                   # Service worker
├── style.css              # CSS with root variables
├── app.js                 # Main JavaScript functionality
├── qr-scanner.js          # QR code scanning
├── php/
│   ├── subscribe.php      # Handle push subscriptions
│   ├── send-notification.php # Send push notifications
│   └── location.php       # Location services
├── subscriptions/         # JSON storage folder
├── icons/                 # PWA icons
└── README.md             # This file
```

## Configuration

### VAPID Keys
For push notifications to work properly, you need to generate VAPID keys:

1. **Generate VAPID keys** using a tool like [web-push-codelab](https://web-push-codelab.glitch.me/)
2. **Update the keys** in:
   - `app.js` - Update `getVapidPublicKey()` function
   - `php/send-notification.php` - Update `$vapidPublicKey` and `$vapidPrivateKey`

### QR Code Scanner
To enable QR code scanning, include the jsQR library:

```html
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
```

Add this script tag to your `index.html` before the closing `</body>` tag.

## Usage

### Subscribe to Notifications
1. Click "Subscribe to Notifications" in the app
2. Allow notification permissions when prompted
3. Your subscription will be saved as a JSON file

### Send Notifications
1. Use the "Send Push Notification" section
2. Fill in title, message, and optional icon URL
3. Click "Send Notification" to send to all subscribers

### Location Services
1. Click "Get Current Location"
2. Allow location permissions when prompted
3. View your coordinates and address information

### QR Code Scanner
1. Click "Start QR Scanner"
2. Allow camera permissions when prompted
3. Point camera at QR code to scan

### Image Capture
1. Use "Take Photo" for camera capture
2. Use "Select from Gallery" for file selection
3. View preview and image data

## API Endpoints

### Subscription Management
- `POST /php/subscribe.php` - Subscribe to notifications
- `GET /php/subscribe.php?action=list` - List all subscriptions
- `GET /php/subscribe.php?action=count` - Get subscription count

### Notifications
- `POST /php/send-notification.php` - Send push notification

### Location Services
- `POST /php/location.php` - Save location data
- `GET /php/location.php?action=reverse_geocode&lat=X&lng=Y` - Reverse geocode
- `GET /php/location.php?action=nearby&lat=X&lng=Y&radius=1000` - Find nearby locations

## Security Considerations

1. **HTTPS Required** - PWA features require HTTPS in production
2. **VAPID Keys** - Store private keys securely
3. **Input Validation** - All user inputs are validated
4. **File Permissions** - Ensure proper folder permissions
5. **Rate Limiting** - Consider implementing rate limiting for API endpoints

## Browser Support

- **Chrome/Edge** - Full support
- **Firefox** - Full support
- **Safari** - Partial support (no push notifications on iOS)
- **Mobile Browsers** - Full support on Android, partial on iOS

## Development

### Local Development
1. Use a local web server with PHP support
2. For HTTPS in development, use tools like:
   - XAMPP/WAMP with SSL
   - PHP built-in server with ngrok
   - Docker with SSL certificates

### Testing Push Notifications
1. Subscribe to notifications in the app
2. Use the test notification feature
3. Check browser developer tools for debugging

## Troubleshooting

### Push Notifications Not Working
- Ensure HTTPS is enabled
- Check VAPID keys are correct
- Verify notification permissions are granted
- Check browser console for errors

### QR Scanner Not Working
- Ensure HTTPS is enabled (required for camera access)
- Check camera permissions are granted
- Include the jsQR library
- Test on different devices/browsers

### Location Services Not Working
- Ensure HTTPS is enabled
- Check location permissions are granted
- Test on different devices
- Check for GPS/location services enabled

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues, feature requests, and pull requests to improve this PWA.
