<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Handle different request methods
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    // Handle GET requests for subscription management
    handleGetRequest();
    exit();
} elseif ($method !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $subscription = json_decode($input, true);
    
    if (!$subscription) {
        throw new Exception('Invalid JSON data');
    }
    
    // Validate subscription data
    if (!isset($subscription['endpoint']) || !isset($subscription['keys'])) {
        throw new Exception('Invalid subscription data');
    }
    
    // Generate unique subscription ID
    $subscriptionId = generateSubscriptionId($subscription['endpoint']);
    
    // Prepare subscription data with metadata
    $subscriptionData = [
        'id' => $subscriptionId,
        'endpoint' => $subscription['endpoint'],
        'keys' => $subscription['keys'],
        'created_at' => date('Y-m-d H:i:s'),
        'last_used' => date('Y-m-d H:i:s'),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
        'ip_address' => getClientIP(),
        'active' => true
    ];
    
    // Save subscription to JSON file
    $filename = '../subscriptions/subscription_' . $subscriptionId . '.json';
    $result = file_put_contents($filename, json_encode($subscriptionData, JSON_PRETTY_PRINT));
    
    if ($result === false) {
        throw new Exception('Failed to save subscription');
    }
    
    // Log subscription
    logSubscription($subscriptionId, 'CREATED');
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Subscription saved successfully',
        'subscription_id' => $subscriptionId,
        'created_at' => $subscriptionData['created_at']
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
    
    // Log error
    error_log('Subscription error: ' . $e->getMessage());
}

/**
 * Generate unique subscription ID from endpoint
 */
function generateSubscriptionId($endpoint) {
    return substr(md5($endpoint . time()), 0, 16);
}

/**
 * Get client IP address
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            return trim($ips[0]);
        }
    }
    
    return 'Unknown';
}

/**
 * Log subscription activity
 */
function logSubscription($subscriptionId, $action) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'subscription_id' => $subscriptionId,
        'action' => $action,
        'ip_address' => getClientIP(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
    ];
    
    $logFile = '../subscriptions/activity.log';
    $logLine = json_encode($logEntry) . "\n";
    
    file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
}

/**
 * Get all active subscriptions
 */
function getActiveSubscriptions() {
    $subscriptions = [];
    $subscriptionDir = '../subscriptions/';
    
    if (!is_dir($subscriptionDir)) {
        return $subscriptions;
    }
    
    $files = glob($subscriptionDir . 'subscription_*.json');
    
    foreach ($files as $file) {
        $data = json_decode(file_get_contents($file), true);
        if ($data && isset($data['active']) && $data['active']) {
            $subscriptions[] = $data;
        }
    }
    
    return $subscriptions;
}

/**
 * Clean up old or invalid subscriptions
 */
function cleanupSubscriptions() {
    $subscriptionDir = '../subscriptions/';
    $files = glob($subscriptionDir . 'subscription_*.json');
    $cleaned = 0;
    
    foreach ($files as $file) {
        $data = json_decode(file_get_contents($file), true);
        
        if (!$data) {
            unlink($file);
            $cleaned++;
            continue;
        }
        
        // Remove subscriptions older than 30 days and inactive
        $createdAt = strtotime($data['created_at']);
        $thirtyDaysAgo = time() - (30 * 24 * 60 * 60);
        
        if ($createdAt < $thirtyDaysAgo && (!isset($data['active']) || !$data['active'])) {
            unlink($file);
            $cleaned++;
        }
    }
    
    return $cleaned;
}

/**
 * Handle GET requests for subscription management
 */
function handleGetRequest() {
    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'list':
            echo json_encode(getActiveSubscriptions());
            break;

        case 'cleanup':
            $cleaned = cleanupSubscriptions();
            echo json_encode(['cleaned' => $cleaned]);
            break;

        case 'count':
            $subscriptions = getActiveSubscriptions();
            echo json_encode(['count' => count($subscriptions)]);
            break;

        default:
            echo json_encode(['error' => 'Invalid action']);
    }
}
?>
