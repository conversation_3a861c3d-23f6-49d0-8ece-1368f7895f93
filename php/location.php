<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'POST') {
        // Handle location data submission
        handleLocationSubmission();
    } elseif ($method === 'GET') {
        // Handle location queries
        handleLocationQuery();
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
    
    error_log('Location service error: ' . $e->getMessage());
}

/**
 * Handle location data submission
 */
function handleLocationSubmission() {
    $input = file_get_contents('php://input');
    $locationData = json_decode($input, true);
    
    if (!$locationData) {
        throw new Exception('Invalid JSON data');
    }
    
    // Validate location data
    if (!isset($locationData['latitude']) || !isset($locationData['longitude'])) {
        throw new Exception('Latitude and longitude are required');
    }
    
    $lat = floatval($locationData['latitude']);
    $lng = floatval($locationData['longitude']);
    
    // Validate coordinates
    if ($lat < -90 || $lat > 90 || $lng < -180 || $lng > 180) {
        throw new Exception('Invalid coordinates');
    }
    
    // Prepare location record
    $locationRecord = [
        'id' => generateLocationId(),
        'latitude' => $lat,
        'longitude' => $lng,
        'accuracy' => $locationData['accuracy'] ?? null,
        'altitude' => $locationData['altitude'] ?? null,
        'heading' => $locationData['heading'] ?? null,
        'speed' => $locationData['speed'] ?? null,
        'timestamp' => date('Y-m-d H:i:s'),
        'ip_address' => getClientIP(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
    ];
    
    // Reverse geocode if requested
    if (isset($locationData['reverse_geocode']) && $locationData['reverse_geocode']) {
        $address = reverseGeocode($lat, $lng);
        $locationRecord['address'] = $address;
    }
    
    // Save location data
    $filename = '../subscriptions/location_' . $locationRecord['id'] . '.json';
    $result = file_put_contents($filename, json_encode($locationRecord, JSON_PRETTY_PRINT));
    
    if ($result === false) {
        throw new Exception('Failed to save location data');
    }
    
    // Log location
    logLocationActivity($locationRecord['id'], 'SAVED');
    
    echo json_encode([
        'success' => true,
        'message' => 'Location saved successfully',
        'location_id' => $locationRecord['id'],
        'address' => $locationRecord['address'] ?? null
    ]);
}

/**
 * Handle location queries
 */
function handleLocationQuery() {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'reverse_geocode':
            $lat = $_GET['lat'] ?? '';
            $lng = $_GET['lng'] ?? '';
            
            if (!$lat || !$lng) {
                throw new Exception('Latitude and longitude are required');
            }
            
            $address = reverseGeocode(floatval($lat), floatval($lng));
            echo json_encode(['address' => $address]);
            break;
            
        case 'nearby':
            $lat = $_GET['lat'] ?? '';
            $lng = $_GET['lng'] ?? '';
            $radius = $_GET['radius'] ?? 1000; // Default 1km
            
            if (!$lat || !$lng) {
                throw new Exception('Latitude and longitude are required');
            }
            
            $nearby = findNearbyLocations(floatval($lat), floatval($lng), floatval($radius));
            echo json_encode(['nearby_locations' => $nearby]);
            break;
            
        case 'list':
            $locations = getStoredLocations();
            echo json_encode(['locations' => $locations]);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
}

/**
 * Reverse geocode coordinates to address
 */
function reverseGeocode($lat, $lng) {
    // This is a simplified reverse geocoding function
    // In production, you would use a service like:
    // - Google Maps Geocoding API
    // - OpenStreetMap Nominatim
    // - MapBox Geocoding API
    
    // For demo purposes, we'll return a mock address
    $mockAddresses = [
        'New York, NY, USA',
        'Los Angeles, CA, USA',
        'Chicago, IL, USA',
        'Houston, TX, USA',
        'Phoenix, AZ, USA',
        'Philadelphia, PA, USA',
        'San Antonio, TX, USA',
        'San Diego, CA, USA',
        'Dallas, TX, USA',
        'San Jose, CA, USA'
    ];
    
    // Return a random mock address for demo
    $randomIndex = abs(crc32($lat . $lng)) % count($mockAddresses);
    $mockAddress = $mockAddresses[$randomIndex];
    
    // Add coordinates to the address
    $address = [
        'formatted_address' => $mockAddress,
        'latitude' => $lat,
        'longitude' => $lng,
        'components' => [
            'street_number' => rand(100, 9999),
            'route' => 'Main Street',
            'locality' => explode(',', $mockAddress)[0],
            'administrative_area_level_1' => explode(',', $mockAddress)[1] ?? '',
            'country' => 'USA',
            'postal_code' => sprintf('%05d', rand(10000, 99999))
        ]
    ];
    
    return $address;
}

/**
 * Find nearby stored locations
 */
function findNearbyLocations($lat, $lng, $radiusMeters) {
    $locations = getStoredLocations();
    $nearby = [];
    
    foreach ($locations as $location) {
        $distance = calculateDistance($lat, $lng, $location['latitude'], $location['longitude']);
        
        if ($distance <= $radiusMeters) {
            $location['distance_meters'] = round($distance, 2);
            $nearby[] = $location;
        }
    }
    
    // Sort by distance
    usort($nearby, function($a, $b) {
        return $a['distance_meters'] <=> $b['distance_meters'];
    });
    
    return $nearby;
}

/**
 * Calculate distance between two coordinates using Haversine formula
 */
function calculateDistance($lat1, $lng1, $lat2, $lng2) {
    $earthRadius = 6371000; // Earth's radius in meters
    
    $dLat = deg2rad($lat2 - $lat1);
    $dLng = deg2rad($lng2 - $lng1);
    
    $a = sin($dLat/2) * sin($dLat/2) +
         cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
         sin($dLng/2) * sin($dLng/2);
    
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
    
    return $earthRadius * $c;
}

/**
 * Get all stored locations
 */
function getStoredLocations() {
    $locations = [];
    $locationDir = '../subscriptions/';
    
    if (!is_dir($locationDir)) {
        return $locations;
    }
    
    $files = glob($locationDir . 'location_*.json');
    
    foreach ($files as $file) {
        $data = json_decode(file_get_contents($file), true);
        if ($data) {
            $locations[] = $data;
        }
    }
    
    // Sort by timestamp (newest first)
    usort($locations, function($a, $b) {
        return strtotime($b['timestamp']) - strtotime($a['timestamp']);
    });
    
    return $locations;
}

/**
 * Generate unique location ID
 */
function generateLocationId() {
    return substr(md5(uniqid(rand(), true)), 0, 16);
}

/**
 * Log location activity
 */
function logLocationActivity($locationId, $action) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'location_id' => $locationId,
        'action' => $action,
        'ip_address' => getClientIP(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
    ];
    
    $logFile = '../subscriptions/location.log';
    $logLine = json_encode($logEntry) . "\n";
    
    file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
}

/**
 * Get client IP address
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            return trim($ips[0]);
        }
    }
    
    return 'Unknown';
}
?>
