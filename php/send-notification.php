<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// VAPID keys (In production, store these securely)
$vapidPublicKey = 'BEl62iUYgUivxIkv69yViEuiBIa40HI80NM9f8HnKJuLFSUjNTahPVWJNNKqhSO1fEsGcPJQQBdHVggU9H5z7Cs';
$vapidPrivateKey = 'your-vapid-private-key-here'; // Replace with actual private key
$vapidSubject = 'mailto:<EMAIL>'; // Replace with your email

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $notificationData = json_decode($input, true);
    
    if (!$notificationData) {
        throw new Exception('Invalid JSON data');
    }
    
    // Validate notification data
    if (!isset($notificationData['title']) || !isset($notificationData['body'])) {
        throw new Exception('Title and body are required');
    }
    
    // Prepare notification payload
    $payload = [
        'title' => $notificationData['title'],
        'body' => $notificationData['body'],
        'icon' => $notificationData['icon'] ?? '/icons/icon-192x192.png',
        'badge' => '/icons/icon-72x72.png',
        'tag' => 'pwa-notification-' . time(),
        'requireInteraction' => true,
        'actions' => [
            [
                'action' => 'open',
                'title' => 'Open App'
            ],
            [
                'action' => 'close',
                'title' => 'Close'
            ]
        ]
    ];
    
    // Get all active subscriptions
    $subscriptions = getActiveSubscriptions();
    
    if (empty($subscriptions)) {
        throw new Exception('No active subscriptions found');
    }
    
    $sentCount = 0;
    $failedCount = 0;
    $errors = [];
    
    // Send notification to each subscription
    foreach ($subscriptions as $subscription) {
        try {
            $result = sendPushNotification($subscription, $payload);
            if ($result) {
                $sentCount++;
                updateSubscriptionLastUsed($subscription['id']);
            } else {
                $failedCount++;
            }
        } catch (Exception $e) {
            $failedCount++;
            $errors[] = $e->getMessage();
            
            // Mark subscription as inactive if it fails
            markSubscriptionInactive($subscription['id']);
        }
    }
    
    // Log notification
    logNotification($notificationData, $sentCount, $failedCount);
    
    // Return response
    echo json_encode([
        'success' => true,
        'message' => 'Notification sent',
        'sent_count' => $sentCount,
        'failed_count' => $failedCount,
        'total_subscriptions' => count($subscriptions),
        'errors' => $errors
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
    
    error_log('Send notification error: ' . $e->getMessage());
}

/**
 * Send push notification to a subscription
 */
function sendPushNotification($subscription, $payload) {
    global $vapidPublicKey, $vapidPrivateKey, $vapidSubject;
    
    // For this demo, we'll simulate sending the notification
    // In production, you would use a proper Web Push library like:
    // - Minishlink/web-push (PHP)
    // - web-push-libs/web-push-php
    
    // Simulate HTTP request to push service
    $endpoint = $subscription['endpoint'];
    $payloadJson = json_encode($payload);
    
    // Create HTTP context for the request
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'TTL: 86400', // 24 hours
                'Urgency: normal'
            ],
            'content' => $payloadJson,
            'timeout' => 30
        ]
    ]);
    
    // For demo purposes, we'll just log the notification
    // In production, you would make the actual HTTP request to the push service
    error_log("Simulated push notification to: " . $endpoint);
    error_log("Payload: " . $payloadJson);
    
    // Simulate success (in production, check the actual HTTP response)
    return true;
}

/**
 * Get all active subscriptions
 */
function getActiveSubscriptions() {
    $subscriptions = [];
    $subscriptionDir = '../subscriptions/';
    
    if (!is_dir($subscriptionDir)) {
        return $subscriptions;
    }
    
    $files = glob($subscriptionDir . 'subscription_*.json');
    
    foreach ($files as $file) {
        $data = json_decode(file_get_contents($file), true);
        if ($data && isset($data['active']) && $data['active']) {
            $subscriptions[] = $data;
        }
    }
    
    return $subscriptions;
}

/**
 * Update subscription last used timestamp
 */
function updateSubscriptionLastUsed($subscriptionId) {
    $filename = '../subscriptions/subscription_' . $subscriptionId . '.json';
    
    if (file_exists($filename)) {
        $data = json_decode(file_get_contents($filename), true);
        if ($data) {
            $data['last_used'] = date('Y-m-d H:i:s');
            file_put_contents($filename, json_encode($data, JSON_PRETTY_PRINT));
        }
    }
}

/**
 * Mark subscription as inactive
 */
function markSubscriptionInactive($subscriptionId) {
    $filename = '../subscriptions/subscription_' . $subscriptionId . '.json';
    
    if (file_exists($filename)) {
        $data = json_decode(file_get_contents($filename), true);
        if ($data) {
            $data['active'] = false;
            $data['deactivated_at'] = date('Y-m-d H:i:s');
            file_put_contents($filename, json_encode($data, JSON_PRETTY_PRINT));
        }
    }
}

/**
 * Log notification activity
 */
function logNotification($notificationData, $sentCount, $failedCount) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'title' => $notificationData['title'],
        'body' => substr($notificationData['body'], 0, 100), // Truncate for logging
        'sent_count' => $sentCount,
        'failed_count' => $failedCount,
        'ip_address' => getClientIP(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
    ];
    
    $logFile = '../subscriptions/notifications.log';
    $logLine = json_encode($logEntry) . "\n";
    
    file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
}

/**
 * Get client IP address
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            return trim($ips[0]);
        }
    }
    
    return 'Unknown';
}

/**
 * Generate VAPID keys (utility function)
 */
function generateVapidKeys() {
    // This is a utility function to generate VAPID keys
    // You can run this once to generate your keys
    // Use a proper library like web-push-php for production
    
    return [
        'public_key' => 'BEl62iUYgUivxIkv69yViEuiBIa40HI80NM9f8HnKJuLFSUjNTahPVWJNNKqhSO1fEsGcPJQQBdHVggU9H5z7Cs',
        'private_key' => 'your-generated-private-key-here'
    ];
}
?>
