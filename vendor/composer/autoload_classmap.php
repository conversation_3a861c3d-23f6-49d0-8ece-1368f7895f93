<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Base64Url\\Base64Url' => $vendorDir . '/spomky-labs/base64url/src/Base64Url.php',
    'Brick\\Math\\BigDecimal' => $vendorDir . '/brick/math/src/BigDecimal.php',
    'Brick\\Math\\BigInteger' => $vendorDir . '/brick/math/src/BigInteger.php',
    'Brick\\Math\\BigNumber' => $vendorDir . '/brick/math/src/BigNumber.php',
    'Brick\\Math\\BigRational' => $vendorDir . '/brick/math/src/BigRational.php',
    'Brick\\Math\\Exception\\DivisionByZeroException' => $vendorDir . '/brick/math/src/Exception/DivisionByZeroException.php',
    'Brick\\Math\\Exception\\IntegerOverflowException' => $vendorDir . '/brick/math/src/Exception/IntegerOverflowException.php',
    'Brick\\Math\\Exception\\MathException' => $vendorDir . '/brick/math/src/Exception/MathException.php',
    'Brick\\Math\\Exception\\NegativeNumberException' => $vendorDir . '/brick/math/src/Exception/NegativeNumberException.php',
    'Brick\\Math\\Exception\\NumberFormatException' => $vendorDir . '/brick/math/src/Exception/NumberFormatException.php',
    'Brick\\Math\\Exception\\RoundingNecessaryException' => $vendorDir . '/brick/math/src/Exception/RoundingNecessaryException.php',
    'Brick\\Math\\Internal\\Calculator' => $vendorDir . '/brick/math/src/Internal/Calculator.php',
    'Brick\\Math\\Internal\\Calculator\\BcMathCalculator' => $vendorDir . '/brick/math/src/Internal/Calculator/BcMathCalculator.php',
    'Brick\\Math\\Internal\\Calculator\\GmpCalculator' => $vendorDir . '/brick/math/src/Internal/Calculator/GmpCalculator.php',
    'Brick\\Math\\Internal\\Calculator\\NativeCalculator' => $vendorDir . '/brick/math/src/Internal/Calculator/NativeCalculator.php',
    'Brick\\Math\\RoundingMode' => $vendorDir . '/brick/math/src/RoundingMode.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'GuzzleHttp\\BodySummarizer' => $vendorDir . '/guzzlehttp/guzzle/src/BodySummarizer.php',
    'GuzzleHttp\\BodySummarizerInterface' => $vendorDir . '/guzzlehttp/guzzle/src/BodySummarizerInterface.php',
    'GuzzleHttp\\Client' => $vendorDir . '/guzzlehttp/guzzle/src/Client.php',
    'GuzzleHttp\\ClientInterface' => $vendorDir . '/guzzlehttp/guzzle/src/ClientInterface.php',
    'GuzzleHttp\\ClientTrait' => $vendorDir . '/guzzlehttp/guzzle/src/ClientTrait.php',
    'GuzzleHttp\\Cookie\\CookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/CookieJar.php',
    'GuzzleHttp\\Cookie\\CookieJarInterface' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/CookieJarInterface.php',
    'GuzzleHttp\\Cookie\\FileCookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/FileCookieJar.php',
    'GuzzleHttp\\Cookie\\SessionCookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/SessionCookieJar.php',
    'GuzzleHttp\\Cookie\\SetCookie' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/SetCookie.php',
    'GuzzleHttp\\Exception\\BadResponseException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/BadResponseException.php',
    'GuzzleHttp\\Exception\\ClientException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ClientException.php',
    'GuzzleHttp\\Exception\\ConnectException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ConnectException.php',
    'GuzzleHttp\\Exception\\GuzzleException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/GuzzleException.php',
    'GuzzleHttp\\Exception\\InvalidArgumentException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/InvalidArgumentException.php',
    'GuzzleHttp\\Exception\\RequestException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/RequestException.php',
    'GuzzleHttp\\Exception\\ServerException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ServerException.php',
    'GuzzleHttp\\Exception\\TooManyRedirectsException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/TooManyRedirectsException.php',
    'GuzzleHttp\\Exception\\TransferException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/TransferException.php',
    'GuzzleHttp\\HandlerStack' => $vendorDir . '/guzzlehttp/guzzle/src/HandlerStack.php',
    'GuzzleHttp\\Handler\\CurlFactory' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlFactory.php',
    'GuzzleHttp\\Handler\\CurlFactoryInterface' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlFactoryInterface.php',
    'GuzzleHttp\\Handler\\CurlHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlHandler.php',
    'GuzzleHttp\\Handler\\CurlMultiHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlMultiHandler.php',
    'GuzzleHttp\\Handler\\EasyHandle' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/EasyHandle.php',
    'GuzzleHttp\\Handler\\HeaderProcessor' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/HeaderProcessor.php',
    'GuzzleHttp\\Handler\\MockHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/MockHandler.php',
    'GuzzleHttp\\Handler\\Proxy' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/Proxy.php',
    'GuzzleHttp\\Handler\\StreamHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/StreamHandler.php',
    'GuzzleHttp\\MessageFormatter' => $vendorDir . '/guzzlehttp/guzzle/src/MessageFormatter.php',
    'GuzzleHttp\\MessageFormatterInterface' => $vendorDir . '/guzzlehttp/guzzle/src/MessageFormatterInterface.php',
    'GuzzleHttp\\Middleware' => $vendorDir . '/guzzlehttp/guzzle/src/Middleware.php',
    'GuzzleHttp\\Pool' => $vendorDir . '/guzzlehttp/guzzle/src/Pool.php',
    'GuzzleHttp\\PrepareBodyMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/PrepareBodyMiddleware.php',
    'GuzzleHttp\\Promise\\AggregateException' => $vendorDir . '/guzzlehttp/promises/src/AggregateException.php',
    'GuzzleHttp\\Promise\\CancellationException' => $vendorDir . '/guzzlehttp/promises/src/CancellationException.php',
    'GuzzleHttp\\Promise\\Coroutine' => $vendorDir . '/guzzlehttp/promises/src/Coroutine.php',
    'GuzzleHttp\\Promise\\Create' => $vendorDir . '/guzzlehttp/promises/src/Create.php',
    'GuzzleHttp\\Promise\\Each' => $vendorDir . '/guzzlehttp/promises/src/Each.php',
    'GuzzleHttp\\Promise\\EachPromise' => $vendorDir . '/guzzlehttp/promises/src/EachPromise.php',
    'GuzzleHttp\\Promise\\FulfilledPromise' => $vendorDir . '/guzzlehttp/promises/src/FulfilledPromise.php',
    'GuzzleHttp\\Promise\\Is' => $vendorDir . '/guzzlehttp/promises/src/Is.php',
    'GuzzleHttp\\Promise\\Promise' => $vendorDir . '/guzzlehttp/promises/src/Promise.php',
    'GuzzleHttp\\Promise\\PromiseInterface' => $vendorDir . '/guzzlehttp/promises/src/PromiseInterface.php',
    'GuzzleHttp\\Promise\\PromisorInterface' => $vendorDir . '/guzzlehttp/promises/src/PromisorInterface.php',
    'GuzzleHttp\\Promise\\RejectedPromise' => $vendorDir . '/guzzlehttp/promises/src/RejectedPromise.php',
    'GuzzleHttp\\Promise\\RejectionException' => $vendorDir . '/guzzlehttp/promises/src/RejectionException.php',
    'GuzzleHttp\\Promise\\TaskQueue' => $vendorDir . '/guzzlehttp/promises/src/TaskQueue.php',
    'GuzzleHttp\\Promise\\TaskQueueInterface' => $vendorDir . '/guzzlehttp/promises/src/TaskQueueInterface.php',
    'GuzzleHttp\\Promise\\Utils' => $vendorDir . '/guzzlehttp/promises/src/Utils.php',
    'GuzzleHttp\\Psr7\\AppendStream' => $vendorDir . '/guzzlehttp/psr7/src/AppendStream.php',
    'GuzzleHttp\\Psr7\\BufferStream' => $vendorDir . '/guzzlehttp/psr7/src/BufferStream.php',
    'GuzzleHttp\\Psr7\\CachingStream' => $vendorDir . '/guzzlehttp/psr7/src/CachingStream.php',
    'GuzzleHttp\\Psr7\\DroppingStream' => $vendorDir . '/guzzlehttp/psr7/src/DroppingStream.php',
    'GuzzleHttp\\Psr7\\Exception\\MalformedUriException' => $vendorDir . '/guzzlehttp/psr7/src/Exception/MalformedUriException.php',
    'GuzzleHttp\\Psr7\\FnStream' => $vendorDir . '/guzzlehttp/psr7/src/FnStream.php',
    'GuzzleHttp\\Psr7\\Header' => $vendorDir . '/guzzlehttp/psr7/src/Header.php',
    'GuzzleHttp\\Psr7\\HttpFactory' => $vendorDir . '/guzzlehttp/psr7/src/HttpFactory.php',
    'GuzzleHttp\\Psr7\\InflateStream' => $vendorDir . '/guzzlehttp/psr7/src/InflateStream.php',
    'GuzzleHttp\\Psr7\\LazyOpenStream' => $vendorDir . '/guzzlehttp/psr7/src/LazyOpenStream.php',
    'GuzzleHttp\\Psr7\\LimitStream' => $vendorDir . '/guzzlehttp/psr7/src/LimitStream.php',
    'GuzzleHttp\\Psr7\\Message' => $vendorDir . '/guzzlehttp/psr7/src/Message.php',
    'GuzzleHttp\\Psr7\\MessageTrait' => $vendorDir . '/guzzlehttp/psr7/src/MessageTrait.php',
    'GuzzleHttp\\Psr7\\MimeType' => $vendorDir . '/guzzlehttp/psr7/src/MimeType.php',
    'GuzzleHttp\\Psr7\\MultipartStream' => $vendorDir . '/guzzlehttp/psr7/src/MultipartStream.php',
    'GuzzleHttp\\Psr7\\NoSeekStream' => $vendorDir . '/guzzlehttp/psr7/src/NoSeekStream.php',
    'GuzzleHttp\\Psr7\\PumpStream' => $vendorDir . '/guzzlehttp/psr7/src/PumpStream.php',
    'GuzzleHttp\\Psr7\\Query' => $vendorDir . '/guzzlehttp/psr7/src/Query.php',
    'GuzzleHttp\\Psr7\\Request' => $vendorDir . '/guzzlehttp/psr7/src/Request.php',
    'GuzzleHttp\\Psr7\\Response' => $vendorDir . '/guzzlehttp/psr7/src/Response.php',
    'GuzzleHttp\\Psr7\\Rfc7230' => $vendorDir . '/guzzlehttp/psr7/src/Rfc7230.php',
    'GuzzleHttp\\Psr7\\ServerRequest' => $vendorDir . '/guzzlehttp/psr7/src/ServerRequest.php',
    'GuzzleHttp\\Psr7\\Stream' => $vendorDir . '/guzzlehttp/psr7/src/Stream.php',
    'GuzzleHttp\\Psr7\\StreamDecoratorTrait' => $vendorDir . '/guzzlehttp/psr7/src/StreamDecoratorTrait.php',
    'GuzzleHttp\\Psr7\\StreamWrapper' => $vendorDir . '/guzzlehttp/psr7/src/StreamWrapper.php',
    'GuzzleHttp\\Psr7\\UploadedFile' => $vendorDir . '/guzzlehttp/psr7/src/UploadedFile.php',
    'GuzzleHttp\\Psr7\\Uri' => $vendorDir . '/guzzlehttp/psr7/src/Uri.php',
    'GuzzleHttp\\Psr7\\UriComparator' => $vendorDir . '/guzzlehttp/psr7/src/UriComparator.php',
    'GuzzleHttp\\Psr7\\UriNormalizer' => $vendorDir . '/guzzlehttp/psr7/src/UriNormalizer.php',
    'GuzzleHttp\\Psr7\\UriResolver' => $vendorDir . '/guzzlehttp/psr7/src/UriResolver.php',
    'GuzzleHttp\\Psr7\\Utils' => $vendorDir . '/guzzlehttp/psr7/src/Utils.php',
    'GuzzleHttp\\RedirectMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/RedirectMiddleware.php',
    'GuzzleHttp\\RequestOptions' => $vendorDir . '/guzzlehttp/guzzle/src/RequestOptions.php',
    'GuzzleHttp\\RetryMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/RetryMiddleware.php',
    'GuzzleHttp\\TransferStats' => $vendorDir . '/guzzlehttp/guzzle/src/TransferStats.php',
    'GuzzleHttp\\Utils' => $vendorDir . '/guzzlehttp/guzzle/src/Utils.php',
    'Jose\\Component\\Checker\\AlgorithmChecker' => $vendorDir . '/web-token/jwt-library/Checker/AlgorithmChecker.php',
    'Jose\\Component\\Checker\\AudienceChecker' => $vendorDir . '/web-token/jwt-library/Checker/AudienceChecker.php',
    'Jose\\Component\\Checker\\CallableChecker' => $vendorDir . '/web-token/jwt-library/Checker/CallableChecker.php',
    'Jose\\Component\\Checker\\ClaimChecker' => $vendorDir . '/web-token/jwt-library/Checker/ClaimChecker.php',
    'Jose\\Component\\Checker\\ClaimCheckerManager' => $vendorDir . '/web-token/jwt-library/Checker/ClaimCheckerManager.php',
    'Jose\\Component\\Checker\\ClaimCheckerManagerFactory' => $vendorDir . '/web-token/jwt-library/Checker/ClaimCheckerManagerFactory.php',
    'Jose\\Component\\Checker\\ClaimExceptionInterface' => $vendorDir . '/web-token/jwt-library/Checker/ClaimExceptionInterface.php',
    'Jose\\Component\\Checker\\ExpirationTimeChecker' => $vendorDir . '/web-token/jwt-library/Checker/ExpirationTimeChecker.php',
    'Jose\\Component\\Checker\\HeaderChecker' => $vendorDir . '/web-token/jwt-library/Checker/HeaderChecker.php',
    'Jose\\Component\\Checker\\HeaderCheckerManager' => $vendorDir . '/web-token/jwt-library/Checker/HeaderCheckerManager.php',
    'Jose\\Component\\Checker\\HeaderCheckerManagerFactory' => $vendorDir . '/web-token/jwt-library/Checker/HeaderCheckerManagerFactory.php',
    'Jose\\Component\\Checker\\InternalClock' => $vendorDir . '/web-token/jwt-library/Checker/InternalClock.php',
    'Jose\\Component\\Checker\\InvalidClaimException' => $vendorDir . '/web-token/jwt-library/Checker/InvalidClaimException.php',
    'Jose\\Component\\Checker\\InvalidHeaderException' => $vendorDir . '/web-token/jwt-library/Checker/InvalidHeaderException.php',
    'Jose\\Component\\Checker\\IsEqualChecker' => $vendorDir . '/web-token/jwt-library/Checker/IsEqualChecker.php',
    'Jose\\Component\\Checker\\IssuedAtChecker' => $vendorDir . '/web-token/jwt-library/Checker/IssuedAtChecker.php',
    'Jose\\Component\\Checker\\IssuerChecker' => $vendorDir . '/web-token/jwt-library/Checker/IssuerChecker.php',
    'Jose\\Component\\Checker\\MissingMandatoryClaimException' => $vendorDir . '/web-token/jwt-library/Checker/MissingMandatoryClaimException.php',
    'Jose\\Component\\Checker\\MissingMandatoryHeaderParameterException' => $vendorDir . '/web-token/jwt-library/Checker/MissingMandatoryHeaderParameterException.php',
    'Jose\\Component\\Checker\\NotBeforeChecker' => $vendorDir . '/web-token/jwt-library/Checker/NotBeforeChecker.php',
    'Jose\\Component\\Checker\\TokenTypeSupport' => $vendorDir . '/web-token/jwt-library/Checker/TokenTypeSupport.php',
    'Jose\\Component\\Checker\\UnencodedPayloadChecker' => $vendorDir . '/web-token/jwt-library/Checker/UnencodedPayloadChecker.php',
    'Jose\\Component\\Console\\AddKeyIntoKeysetCommand' => $vendorDir . '/web-token/jwt-library/Console/AddKeyIntoKeysetCommand.php',
    'Jose\\Component\\Console\\EcKeyGeneratorCommand' => $vendorDir . '/web-token/jwt-library/Console/EcKeyGeneratorCommand.php',
    'Jose\\Component\\Console\\EcKeysetGeneratorCommand' => $vendorDir . '/web-token/jwt-library/Console/EcKeysetGeneratorCommand.php',
    'Jose\\Component\\Console\\GeneratorCommand' => $vendorDir . '/web-token/jwt-library/Console/GeneratorCommand.php',
    'Jose\\Component\\Console\\GetThumbprintCommand' => $vendorDir . '/web-token/jwt-library/Console/GetThumbprintCommand.php',
    'Jose\\Component\\Console\\JKULoaderCommand' => $vendorDir . '/web-token/jwt-library/Console/JKULoaderCommand.php',
    'Jose\\Component\\Console\\KeyAnalyzerCommand' => $vendorDir . '/web-token/jwt-library/Console/KeyAnalyzerCommand.php',
    'Jose\\Component\\Console\\KeyFileLoaderCommand' => $vendorDir . '/web-token/jwt-library/Console/KeyFileLoaderCommand.php',
    'Jose\\Component\\Console\\KeysetAnalyzerCommand' => $vendorDir . '/web-token/jwt-library/Console/KeysetAnalyzerCommand.php',
    'Jose\\Component\\Console\\MergeKeysetCommand' => $vendorDir . '/web-token/jwt-library/Console/MergeKeysetCommand.php',
    'Jose\\Component\\Console\\NoneKeyGeneratorCommand' => $vendorDir . '/web-token/jwt-library/Console/NoneKeyGeneratorCommand.php',
    'Jose\\Component\\Console\\ObjectOutputCommand' => $vendorDir . '/web-token/jwt-library/Console/ObjectOutputCommand.php',
    'Jose\\Component\\Console\\OctKeyGeneratorCommand' => $vendorDir . '/web-token/jwt-library/Console/OctKeyGeneratorCommand.php',
    'Jose\\Component\\Console\\OctKeysetGeneratorCommand' => $vendorDir . '/web-token/jwt-library/Console/OctKeysetGeneratorCommand.php',
    'Jose\\Component\\Console\\OkpKeyGeneratorCommand' => $vendorDir . '/web-token/jwt-library/Console/OkpKeyGeneratorCommand.php',
    'Jose\\Component\\Console\\OkpKeysetGeneratorCommand' => $vendorDir . '/web-token/jwt-library/Console/OkpKeysetGeneratorCommand.php',
    'Jose\\Component\\Console\\OptimizeRsaKeyCommand' => $vendorDir . '/web-token/jwt-library/Console/OptimizeRsaKeyCommand.php',
    'Jose\\Component\\Console\\P12CertificateLoaderCommand' => $vendorDir . '/web-token/jwt-library/Console/P12CertificateLoaderCommand.php',
    'Jose\\Component\\Console\\PemConverterCommand' => $vendorDir . '/web-token/jwt-library/Console/PemConverterCommand.php',
    'Jose\\Component\\Console\\PublicKeyCommand' => $vendorDir . '/web-token/jwt-library/Console/PublicKeyCommand.php',
    'Jose\\Component\\Console\\PublicKeysetCommand' => $vendorDir . '/web-token/jwt-library/Console/PublicKeysetCommand.php',
    'Jose\\Component\\Console\\RotateKeysetCommand' => $vendorDir . '/web-token/jwt-library/Console/RotateKeysetCommand.php',
    'Jose\\Component\\Console\\RsaKeyGeneratorCommand' => $vendorDir . '/web-token/jwt-library/Console/RsaKeyGeneratorCommand.php',
    'Jose\\Component\\Console\\RsaKeysetGeneratorCommand' => $vendorDir . '/web-token/jwt-library/Console/RsaKeysetGeneratorCommand.php',
    'Jose\\Component\\Console\\SecretKeyGeneratorCommand' => $vendorDir . '/web-token/jwt-library/Console/SecretKeyGeneratorCommand.php',
    'Jose\\Component\\Console\\X509CertificateLoaderCommand' => $vendorDir . '/web-token/jwt-library/Console/X509CertificateLoaderCommand.php',
    'Jose\\Component\\Console\\X5ULoaderCommand' => $vendorDir . '/web-token/jwt-library/Console/X5ULoaderCommand.php',
    'Jose\\Component\\Core\\Algorithm' => $vendorDir . '/web-token/jwt-library/Core/Algorithm.php',
    'Jose\\Component\\Core\\AlgorithmManager' => $vendorDir . '/web-token/jwt-library/Core/AlgorithmManager.php',
    'Jose\\Component\\Core\\AlgorithmManagerFactory' => $vendorDir . '/web-token/jwt-library/Core/AlgorithmManagerFactory.php',
    'Jose\\Component\\Core\\JWK' => $vendorDir . '/web-token/jwt-library/Core/JWK.php',
    'Jose\\Component\\Core\\JWKSet' => $vendorDir . '/web-token/jwt-library/Core/JWKSet.php',
    'Jose\\Component\\Core\\JWT' => $vendorDir . '/web-token/jwt-library/Core/JWT.php',
    'Jose\\Component\\Core\\Util\\Base64UrlSafe' => $vendorDir . '/web-token/jwt-library/Core/Util/Base64UrlSafe.php',
    'Jose\\Component\\Core\\Util\\BigInteger' => $vendorDir . '/web-token/jwt-library/Core/Util/BigInteger.php',
    'Jose\\Component\\Core\\Util\\ECKey' => $vendorDir . '/web-token/jwt-library/Core/Util/ECKey.php',
    'Jose\\Component\\Core\\Util\\ECSignature' => $vendorDir . '/web-token/jwt-library/Core/Util/ECSignature.php',
    'Jose\\Component\\Core\\Util\\Ecc\\Curve' => $vendorDir . '/web-token/jwt-library/Core/Util/Ecc/Curve.php',
    'Jose\\Component\\Core\\Util\\Ecc\\EcDH' => $vendorDir . '/web-token/jwt-library/Core/Util/Ecc/EcDH.php',
    'Jose\\Component\\Core\\Util\\Ecc\\Math' => $vendorDir . '/web-token/jwt-library/Core/Util/Ecc/Math.php',
    'Jose\\Component\\Core\\Util\\Ecc\\ModularArithmetic' => $vendorDir . '/web-token/jwt-library/Core/Util/Ecc/ModularArithmetic.php',
    'Jose\\Component\\Core\\Util\\Ecc\\NistCurve' => $vendorDir . '/web-token/jwt-library/Core/Util/Ecc/NistCurve.php',
    'Jose\\Component\\Core\\Util\\Ecc\\Point' => $vendorDir . '/web-token/jwt-library/Core/Util/Ecc/Point.php',
    'Jose\\Component\\Core\\Util\\Ecc\\PrivateKey' => $vendorDir . '/web-token/jwt-library/Core/Util/Ecc/PrivateKey.php',
    'Jose\\Component\\Core\\Util\\Ecc\\PublicKey' => $vendorDir . '/web-token/jwt-library/Core/Util/Ecc/PublicKey.php',
    'Jose\\Component\\Core\\Util\\Hash' => $vendorDir . '/web-token/jwt-library/Core/Util/Hash.php',
    'Jose\\Component\\Core\\Util\\JsonConverter' => $vendorDir . '/web-token/jwt-library/Core/Util/JsonConverter.php',
    'Jose\\Component\\Core\\Util\\KeyChecker' => $vendorDir . '/web-token/jwt-library/Core/Util/KeyChecker.php',
    'Jose\\Component\\Core\\Util\\RSAKey' => $vendorDir . '/web-token/jwt-library/Core/Util/RSAKey.php',
    'Jose\\Component\\Encryption\\Algorithm\\ContentEncryptionAlgorithm' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/ContentEncryptionAlgorithm.php',
    'Jose\\Component\\Encryption\\Algorithm\\ContentEncryption\\A128CBCHS256' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/ContentEncryption/A128CBCHS256.php',
    'Jose\\Component\\Encryption\\Algorithm\\ContentEncryption\\A128GCM' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/ContentEncryption/A128GCM.php',
    'Jose\\Component\\Encryption\\Algorithm\\ContentEncryption\\A192CBCHS384' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/ContentEncryption/A192CBCHS384.php',
    'Jose\\Component\\Encryption\\Algorithm\\ContentEncryption\\A192GCM' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/ContentEncryption/A192GCM.php',
    'Jose\\Component\\Encryption\\Algorithm\\ContentEncryption\\A256CBCHS512' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/ContentEncryption/A256CBCHS512.php',
    'Jose\\Component\\Encryption\\Algorithm\\ContentEncryption\\A256GCM' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/ContentEncryption/A256GCM.php',
    'Jose\\Component\\Encryption\\Algorithm\\ContentEncryption\\AESCBCHS' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/ContentEncryption/AESCBCHS.php',
    'Jose\\Component\\Encryption\\Algorithm\\ContentEncryption\\AESGCM' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/ContentEncryption/AESGCM.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryptionAlgorithm' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryptionAlgorithm.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\A128GCMKW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/A128GCMKW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\A128KW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/A128KW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\A192GCMKW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/A192GCMKW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\A192KW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/A192KW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\A256GCMKW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/A256GCMKW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\A256KW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/A256KW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\AESGCMKW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/AESGCMKW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\AESKW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/AESKW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\AbstractECDH' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/AbstractECDH.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\AbstractECDHAESKW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/AbstractECDHAESKW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\Dir' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/Dir.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\DirectEncryption' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/DirectEncryption.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\ECDHES' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/ECDHES.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\ECDHESA128KW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/ECDHESA128KW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\ECDHESA192KW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/ECDHESA192KW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\ECDHESA256KW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/ECDHESA256KW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\ECDHESAESKW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/ECDHESAESKW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\ECDHSS' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/ECDHSS.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\ECDHSSA128KW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/ECDHSSA128KW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\ECDHSSA192KW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/ECDHSSA192KW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\ECDHSSA256KW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/ECDHSSA256KW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\ECDHSSAESKW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/ECDHSSAESKW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\KeyAgreement' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/KeyAgreement.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\KeyAgreementWithKeyWrapping' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/KeyAgreementWithKeyWrapping.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\KeyEncryption' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/KeyEncryption.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\KeyWrapping' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/KeyWrapping.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\PBES2AESKW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/PBES2AESKW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\PBES2HS256A128KW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/PBES2HS256A128KW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\PBES2HS384A192KW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/PBES2HS384A192KW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\PBES2HS512A256KW' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/PBES2HS512A256KW.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\RSA' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/RSA.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\RSA15' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/RSA15.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\RSAOAEP' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/RSAOAEP.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\RSAOAEP256' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/RSAOAEP256.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\Util\\ConcatKDF' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/Util/ConcatKDF.php',
    'Jose\\Component\\Encryption\\Algorithm\\KeyEncryption\\Util\\RSACrypt' => $vendorDir . '/web-token/jwt-library/Encryption/Algorithm/KeyEncryption/Util/RSACrypt.php',
    'Jose\\Component\\Encryption\\Compression\\CompressionMethod' => $vendorDir . '/web-token/jwt-library/Encryption/Compression/CompressionMethod.php',
    'Jose\\Component\\Encryption\\Compression\\CompressionMethodManager' => $vendorDir . '/web-token/jwt-library/Encryption/Compression/CompressionMethodManager.php',
    'Jose\\Component\\Encryption\\Compression\\CompressionMethodManagerFactory' => $vendorDir . '/web-token/jwt-library/Encryption/Compression/CompressionMethodManagerFactory.php',
    'Jose\\Component\\Encryption\\Compression\\Deflate' => $vendorDir . '/web-token/jwt-library/Encryption/Compression/Deflate.php',
    'Jose\\Component\\Encryption\\JWE' => $vendorDir . '/web-token/jwt-library/Encryption/JWE.php',
    'Jose\\Component\\Encryption\\JWEBuilder' => $vendorDir . '/web-token/jwt-library/Encryption/JWEBuilder.php',
    'Jose\\Component\\Encryption\\JWEBuilderFactory' => $vendorDir . '/web-token/jwt-library/Encryption/JWEBuilderFactory.php',
    'Jose\\Component\\Encryption\\JWEDecrypter' => $vendorDir . '/web-token/jwt-library/Encryption/JWEDecrypter.php',
    'Jose\\Component\\Encryption\\JWEDecrypterFactory' => $vendorDir . '/web-token/jwt-library/Encryption/JWEDecrypterFactory.php',
    'Jose\\Component\\Encryption\\JWELoader' => $vendorDir . '/web-token/jwt-library/Encryption/JWELoader.php',
    'Jose\\Component\\Encryption\\JWELoaderFactory' => $vendorDir . '/web-token/jwt-library/Encryption/JWELoaderFactory.php',
    'Jose\\Component\\Encryption\\JWETokenSupport' => $vendorDir . '/web-token/jwt-library/Encryption/JWETokenSupport.php',
    'Jose\\Component\\Encryption\\Recipient' => $vendorDir . '/web-token/jwt-library/Encryption/Recipient.php',
    'Jose\\Component\\Encryption\\Serializer\\CompactSerializer' => $vendorDir . '/web-token/jwt-library/Encryption/Serializer/CompactSerializer.php',
    'Jose\\Component\\Encryption\\Serializer\\JSONFlattenedSerializer' => $vendorDir . '/web-token/jwt-library/Encryption/Serializer/JSONFlattenedSerializer.php',
    'Jose\\Component\\Encryption\\Serializer\\JSONGeneralSerializer' => $vendorDir . '/web-token/jwt-library/Encryption/Serializer/JSONGeneralSerializer.php',
    'Jose\\Component\\Encryption\\Serializer\\JWESerializer' => $vendorDir . '/web-token/jwt-library/Encryption/Serializer/JWESerializer.php',
    'Jose\\Component\\Encryption\\Serializer\\JWESerializerManager' => $vendorDir . '/web-token/jwt-library/Encryption/Serializer/JWESerializerManager.php',
    'Jose\\Component\\Encryption\\Serializer\\JWESerializerManagerFactory' => $vendorDir . '/web-token/jwt-library/Encryption/Serializer/JWESerializerManagerFactory.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\AlgorithmAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/AlgorithmAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\ES256KeyAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/ES256KeyAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\ES384KeyAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/ES384KeyAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\ES512KeyAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/ES512KeyAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\ESKeyAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/ESKeyAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\HS256KeyAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/HS256KeyAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\HS384KeyAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/HS384KeyAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\HS512KeyAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/HS512KeyAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\HSKeyAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/HSKeyAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\KeyAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/KeyAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\KeyAnalyzerManager' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/KeyAnalyzerManager.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\KeyIdentifierAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/KeyIdentifierAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\KeysetAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/KeysetAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\KeysetAnalyzerManager' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/KeysetAnalyzerManager.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\Message' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/Message.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\MessageBag' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/MessageBag.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\MixedKeyTypes' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/MixedKeyTypes.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\MixedPublicAndPrivateKeys' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/MixedPublicAndPrivateKeys.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\NoneAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/NoneAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\OctAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/OctAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\RsaAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/RsaAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\UsageAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/UsageAnalyzer.php',
    'Jose\\Component\\KeyManagement\\Analyzer\\ZxcvbnKeyAnalyzer' => $vendorDir . '/web-token/jwt-library/KeyManagement/Analyzer/ZxcvbnKeyAnalyzer.php',
    'Jose\\Component\\KeyManagement\\JKUFactory' => $vendorDir . '/web-token/jwt-library/KeyManagement/JKUFactory.php',
    'Jose\\Component\\KeyManagement\\JWKFactory' => $vendorDir . '/web-token/jwt-library/KeyManagement/JWKFactory.php',
    'Jose\\Component\\KeyManagement\\KeyConverter\\ECKey' => $vendorDir . '/web-token/jwt-library/KeyManagement/KeyConverter/ECKey.php',
    'Jose\\Component\\KeyManagement\\KeyConverter\\KeyConverter' => $vendorDir . '/web-token/jwt-library/KeyManagement/KeyConverter/KeyConverter.php',
    'Jose\\Component\\KeyManagement\\KeyConverter\\RSAKey' => $vendorDir . '/web-token/jwt-library/KeyManagement/KeyConverter/RSAKey.php',
    'Jose\\Component\\KeyManagement\\UrlKeySetFactory' => $vendorDir . '/web-token/jwt-library/KeyManagement/UrlKeySetFactory.php',
    'Jose\\Component\\KeyManagement\\X5UFactory' => $vendorDir . '/web-token/jwt-library/KeyManagement/X5UFactory.php',
    'Jose\\Component\\NestedToken\\NestedTokenBuilder' => $vendorDir . '/web-token/jwt-library/NestedToken/NestedTokenBuilder.php',
    'Jose\\Component\\NestedToken\\NestedTokenBuilderFactory' => $vendorDir . '/web-token/jwt-library/NestedToken/NestedTokenBuilderFactory.php',
    'Jose\\Component\\NestedToken\\NestedTokenLoader' => $vendorDir . '/web-token/jwt-library/NestedToken/NestedTokenLoader.php',
    'Jose\\Component\\NestedToken\\NestedTokenLoaderFactory' => $vendorDir . '/web-token/jwt-library/NestedToken/NestedTokenLoaderFactory.php',
    'Jose\\Component\\Signature\\Algorithm\\ECDSA' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/ECDSA.php',
    'Jose\\Component\\Signature\\Algorithm\\ES256' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/ES256.php',
    'Jose\\Component\\Signature\\Algorithm\\ES384' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/ES384.php',
    'Jose\\Component\\Signature\\Algorithm\\ES512' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/ES512.php',
    'Jose\\Component\\Signature\\Algorithm\\EdDSA' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/EdDSA.php',
    'Jose\\Component\\Signature\\Algorithm\\HMAC' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/HMAC.php',
    'Jose\\Component\\Signature\\Algorithm\\HS256' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/HS256.php',
    'Jose\\Component\\Signature\\Algorithm\\HS384' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/HS384.php',
    'Jose\\Component\\Signature\\Algorithm\\HS512' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/HS512.php',
    'Jose\\Component\\Signature\\Algorithm\\MacAlgorithm' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/MacAlgorithm.php',
    'Jose\\Component\\Signature\\Algorithm\\None' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/None.php',
    'Jose\\Component\\Signature\\Algorithm\\PS256' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/PS256.php',
    'Jose\\Component\\Signature\\Algorithm\\PS384' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/PS384.php',
    'Jose\\Component\\Signature\\Algorithm\\PS512' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/PS512.php',
    'Jose\\Component\\Signature\\Algorithm\\RS256' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/RS256.php',
    'Jose\\Component\\Signature\\Algorithm\\RS384' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/RS384.php',
    'Jose\\Component\\Signature\\Algorithm\\RS512' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/RS512.php',
    'Jose\\Component\\Signature\\Algorithm\\RSAPKCS1' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/RSAPKCS1.php',
    'Jose\\Component\\Signature\\Algorithm\\RSAPSS' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/RSAPSS.php',
    'Jose\\Component\\Signature\\Algorithm\\SignatureAlgorithm' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/SignatureAlgorithm.php',
    'Jose\\Component\\Signature\\Algorithm\\Util\\RSA' => $vendorDir . '/web-token/jwt-library/Signature/Algorithm/Util/RSA.php',
    'Jose\\Component\\Signature\\JWS' => $vendorDir . '/web-token/jwt-library/Signature/JWS.php',
    'Jose\\Component\\Signature\\JWSBuilder' => $vendorDir . '/web-token/jwt-library/Signature/JWSBuilder.php',
    'Jose\\Component\\Signature\\JWSBuilderFactory' => $vendorDir . '/web-token/jwt-library/Signature/JWSBuilderFactory.php',
    'Jose\\Component\\Signature\\JWSLoader' => $vendorDir . '/web-token/jwt-library/Signature/JWSLoader.php',
    'Jose\\Component\\Signature\\JWSLoaderFactory' => $vendorDir . '/web-token/jwt-library/Signature/JWSLoaderFactory.php',
    'Jose\\Component\\Signature\\JWSTokenSupport' => $vendorDir . '/web-token/jwt-library/Signature/JWSTokenSupport.php',
    'Jose\\Component\\Signature\\JWSVerifier' => $vendorDir . '/web-token/jwt-library/Signature/JWSVerifier.php',
    'Jose\\Component\\Signature\\JWSVerifierFactory' => $vendorDir . '/web-token/jwt-library/Signature/JWSVerifierFactory.php',
    'Jose\\Component\\Signature\\Serializer\\CompactSerializer' => $vendorDir . '/web-token/jwt-library/Signature/Serializer/CompactSerializer.php',
    'Jose\\Component\\Signature\\Serializer\\JSONFlattenedSerializer' => $vendorDir . '/web-token/jwt-library/Signature/Serializer/JSONFlattenedSerializer.php',
    'Jose\\Component\\Signature\\Serializer\\JSONGeneralSerializer' => $vendorDir . '/web-token/jwt-library/Signature/Serializer/JSONGeneralSerializer.php',
    'Jose\\Component\\Signature\\Serializer\\JWSSerializer' => $vendorDir . '/web-token/jwt-library/Signature/Serializer/JWSSerializer.php',
    'Jose\\Component\\Signature\\Serializer\\JWSSerializerManager' => $vendorDir . '/web-token/jwt-library/Signature/Serializer/JWSSerializerManager.php',
    'Jose\\Component\\Signature\\Serializer\\JWSSerializerManagerFactory' => $vendorDir . '/web-token/jwt-library/Signature/Serializer/JWSSerializerManagerFactory.php',
    'Jose\\Component\\Signature\\Serializer\\Serializer' => $vendorDir . '/web-token/jwt-library/Signature/Serializer/Serializer.php',
    'Jose\\Component\\Signature\\Signature' => $vendorDir . '/web-token/jwt-library/Signature/Signature.php',
    'Minishlink\\WebPush\\Encryption' => $vendorDir . '/minishlink/web-push/src/Encryption.php',
    'Minishlink\\WebPush\\MessageSentReport' => $vendorDir . '/minishlink/web-push/src/MessageSentReport.php',
    'Minishlink\\WebPush\\Notification' => $vendorDir . '/minishlink/web-push/src/Notification.php',
    'Minishlink\\WebPush\\Subscription' => $vendorDir . '/minishlink/web-push/src/Subscription.php',
    'Minishlink\\WebPush\\SubscriptionInterface' => $vendorDir . '/minishlink/web-push/src/SubscriptionInterface.php',
    'Minishlink\\WebPush\\Utils' => $vendorDir . '/minishlink/web-push/src/Utils.php',
    'Minishlink\\WebPush\\VAPID' => $vendorDir . '/minishlink/web-push/src/VAPID.php',
    'Minishlink\\WebPush\\WebPush' => $vendorDir . '/minishlink/web-push/src/WebPush.php',
    'Normalizer' => $vendorDir . '/symfony/polyfill-intl-normalizer/Resources/stubs/Normalizer.php',
    'ParagonIE\\ConstantTime\\Base32' => $vendorDir . '/paragonie/constant_time_encoding/src/Base32.php',
    'ParagonIE\\ConstantTime\\Base32Hex' => $vendorDir . '/paragonie/constant_time_encoding/src/Base32Hex.php',
    'ParagonIE\\ConstantTime\\Base64' => $vendorDir . '/paragonie/constant_time_encoding/src/Base64.php',
    'ParagonIE\\ConstantTime\\Base64DotSlash' => $vendorDir . '/paragonie/constant_time_encoding/src/Base64DotSlash.php',
    'ParagonIE\\ConstantTime\\Base64DotSlashOrdered' => $vendorDir . '/paragonie/constant_time_encoding/src/Base64DotSlashOrdered.php',
    'ParagonIE\\ConstantTime\\Base64UrlSafe' => $vendorDir . '/paragonie/constant_time_encoding/src/Base64UrlSafe.php',
    'ParagonIE\\ConstantTime\\Binary' => $vendorDir . '/paragonie/constant_time_encoding/src/Binary.php',
    'ParagonIE\\ConstantTime\\EncoderInterface' => $vendorDir . '/paragonie/constant_time_encoding/src/EncoderInterface.php',
    'ParagonIE\\ConstantTime\\Encoding' => $vendorDir . '/paragonie/constant_time_encoding/src/Encoding.php',
    'ParagonIE\\ConstantTime\\Hex' => $vendorDir . '/paragonie/constant_time_encoding/src/Hex.php',
    'ParagonIE\\ConstantTime\\RFC4648' => $vendorDir . '/paragonie/constant_time_encoding/src/RFC4648.php',
    'Psr\\Cache\\CacheException' => $vendorDir . '/psr/cache/src/CacheException.php',
    'Psr\\Cache\\CacheItemInterface' => $vendorDir . '/psr/cache/src/CacheItemInterface.php',
    'Psr\\Cache\\CacheItemPoolInterface' => $vendorDir . '/psr/cache/src/CacheItemPoolInterface.php',
    'Psr\\Cache\\InvalidArgumentException' => $vendorDir . '/psr/cache/src/InvalidArgumentException.php',
    'Psr\\Clock\\ClockInterface' => $vendorDir . '/psr/clock/src/ClockInterface.php',
    'Psr\\Container\\ContainerExceptionInterface' => $vendorDir . '/psr/container/src/ContainerExceptionInterface.php',
    'Psr\\Container\\ContainerInterface' => $vendorDir . '/psr/container/src/ContainerInterface.php',
    'Psr\\Container\\NotFoundExceptionInterface' => $vendorDir . '/psr/container/src/NotFoundExceptionInterface.php',
    'Psr\\Http\\Client\\ClientExceptionInterface' => $vendorDir . '/psr/http-client/src/ClientExceptionInterface.php',
    'Psr\\Http\\Client\\ClientInterface' => $vendorDir . '/psr/http-client/src/ClientInterface.php',
    'Psr\\Http\\Client\\NetworkExceptionInterface' => $vendorDir . '/psr/http-client/src/NetworkExceptionInterface.php',
    'Psr\\Http\\Client\\RequestExceptionInterface' => $vendorDir . '/psr/http-client/src/RequestExceptionInterface.php',
    'Psr\\Http\\Message\\MessageInterface' => $vendorDir . '/psr/http-message/src/MessageInterface.php',
    'Psr\\Http\\Message\\RequestFactoryInterface' => $vendorDir . '/psr/http-factory/src/RequestFactoryInterface.php',
    'Psr\\Http\\Message\\RequestInterface' => $vendorDir . '/psr/http-message/src/RequestInterface.php',
    'Psr\\Http\\Message\\ResponseFactoryInterface' => $vendorDir . '/psr/http-factory/src/ResponseFactoryInterface.php',
    'Psr\\Http\\Message\\ResponseInterface' => $vendorDir . '/psr/http-message/src/ResponseInterface.php',
    'Psr\\Http\\Message\\ServerRequestFactoryInterface' => $vendorDir . '/psr/http-factory/src/ServerRequestFactoryInterface.php',
    'Psr\\Http\\Message\\ServerRequestInterface' => $vendorDir . '/psr/http-message/src/ServerRequestInterface.php',
    'Psr\\Http\\Message\\StreamFactoryInterface' => $vendorDir . '/psr/http-factory/src/StreamFactoryInterface.php',
    'Psr\\Http\\Message\\StreamInterface' => $vendorDir . '/psr/http-message/src/StreamInterface.php',
    'Psr\\Http\\Message\\UploadedFileFactoryInterface' => $vendorDir . '/psr/http-factory/src/UploadedFileFactoryInterface.php',
    'Psr\\Http\\Message\\UploadedFileInterface' => $vendorDir . '/psr/http-message/src/UploadedFileInterface.php',
    'Psr\\Http\\Message\\UriFactoryInterface' => $vendorDir . '/psr/http-factory/src/UriFactoryInterface.php',
    'Psr\\Http\\Message\\UriInterface' => $vendorDir . '/psr/http-message/src/UriInterface.php',
    'Psr\\Log\\AbstractLogger' => $vendorDir . '/psr/log/src/AbstractLogger.php',
    'Psr\\Log\\InvalidArgumentException' => $vendorDir . '/psr/log/src/InvalidArgumentException.php',
    'Psr\\Log\\LogLevel' => $vendorDir . '/psr/log/src/LogLevel.php',
    'Psr\\Log\\LoggerAwareInterface' => $vendorDir . '/psr/log/src/LoggerAwareInterface.php',
    'Psr\\Log\\LoggerAwareTrait' => $vendorDir . '/psr/log/src/LoggerAwareTrait.php',
    'Psr\\Log\\LoggerInterface' => $vendorDir . '/psr/log/src/LoggerInterface.php',
    'Psr\\Log\\LoggerTrait' => $vendorDir . '/psr/log/src/LoggerTrait.php',
    'Psr\\Log\\NullLogger' => $vendorDir . '/psr/log/src/NullLogger.php',
    'SpomkyLabs\\Pki\\ASN1\\Component\\Identifier' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Component/Identifier.php',
    'SpomkyLabs\\Pki\\ASN1\\Component\\Length' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Component/Length.php',
    'SpomkyLabs\\Pki\\ASN1\\DERData' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/DERData.php',
    'SpomkyLabs\\Pki\\ASN1\\Element' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Element.php',
    'SpomkyLabs\\Pki\\ASN1\\Exception\\DecodeException' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Exception/DecodeException.php',
    'SpomkyLabs\\Pki\\ASN1\\Feature\\ElementBase' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Feature/ElementBase.php',
    'SpomkyLabs\\Pki\\ASN1\\Feature\\Encodable' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Feature/Encodable.php',
    'SpomkyLabs\\Pki\\ASN1\\Feature\\Stringable' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Feature/Stringable.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\BaseString' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/BaseString.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\BaseTime' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/BaseTime.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Constructed\\ConstructedString' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Constructed/ConstructedString.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Constructed\\Sequence' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Constructed/Sequence.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Constructed\\Set' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Constructed/Set.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\PrimitiveString' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/PrimitiveString.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\PrimitiveType' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/PrimitiveType.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\BMPString' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/BMPString.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\BitString' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/BitString.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\Boolean' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/Boolean.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\CharacterString' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/CharacterString.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\EOC' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/EOC.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\Enumerated' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/Enumerated.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\GeneralString' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/GeneralString.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\GeneralizedTime' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/GeneralizedTime.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\GraphicString' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/GraphicString.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\IA5String' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/IA5String.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\Integer' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/Integer.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\NullType' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/NullType.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\Number' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/Number.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\NumericString' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/NumericString.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\ObjectDescriptor' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/ObjectDescriptor.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\ObjectIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/ObjectIdentifier.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\OctetString' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/OctetString.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\PrintableString' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/PrintableString.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\Real' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/Real.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\RelativeOID' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/RelativeOID.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\T61String' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/T61String.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\UTCTime' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/UTCTime.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\UTF8String' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/UTF8String.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\UniversalString' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/UniversalString.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\VideotexString' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/VideotexString.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Primitive\\VisibleString' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Primitive/VisibleString.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\StringType' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/StringType.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Structure' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Structure.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\TaggedType' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/TaggedType.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Tagged\\ApplicationType' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Tagged/ApplicationType.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Tagged\\ContextSpecificType' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Tagged/ContextSpecificType.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Tagged\\DERTaggedType' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Tagged/DERTaggedType.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Tagged\\ExplicitTagging' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Tagged/ExplicitTagging.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Tagged\\ExplicitlyTaggedType' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Tagged/ExplicitlyTaggedType.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Tagged\\ImplicitTagging' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Tagged/ImplicitTagging.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Tagged\\ImplicitlyTaggedType' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Tagged/ImplicitlyTaggedType.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Tagged\\PrivateType' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Tagged/PrivateType.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\Tagged\\TaggedTypeWrap' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/Tagged/TaggedTypeWrap.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\TimeType' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/TimeType.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\UniversalClass' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/UniversalClass.php',
    'SpomkyLabs\\Pki\\ASN1\\Type\\UnspecifiedType' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Type/UnspecifiedType.php',
    'SpomkyLabs\\Pki\\ASN1\\Util\\BigInt' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Util/BigInt.php',
    'SpomkyLabs\\Pki\\ASN1\\Util\\Flags' => $vendorDir . '/spomky-labs/pki-framework/src/ASN1/Util/Flags.php',
    'SpomkyLabs\\Pki\\CryptoBridge\\Crypto' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoBridge/Crypto.php',
    'SpomkyLabs\\Pki\\CryptoBridge\\Crypto\\OpenSSLCrypto' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoBridge/Crypto/OpenSSLCrypto.php',
    'SpomkyLabs\\Pki\\CryptoEncoding\\PEM' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoEncoding/PEM.php',
    'SpomkyLabs\\Pki\\CryptoEncoding\\PEMBundle' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoEncoding/PEMBundle.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\AlgorithmIdentifierFactory' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/AlgorithmIdentifierFactory.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\AlgorithmIdentifierProvider' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/AlgorithmIdentifierProvider.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Asymmetric\\ECPublicKeyAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Asymmetric/ECPublicKeyAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Asymmetric\\Ed25519AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Asymmetric/Ed25519AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Asymmetric\\Ed448AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Asymmetric/Ed448AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Asymmetric\\RFC8410EdAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Asymmetric/RFC8410EdAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Asymmetric\\RFC8410XAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Asymmetric/RFC8410XAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Asymmetric\\RSAEncryptionAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Asymmetric/RSAEncryptionAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Asymmetric\\RSAPSSSSAEncryptionAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Asymmetric/RSAPSSSSAEncryptionAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Asymmetric\\X25519AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Asymmetric/X25519AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Asymmetric\\X448AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Asymmetric/X448AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Cipher\\AES128CBCAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Cipher/AES128CBCAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Cipher\\AES192CBCAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Cipher/AES192CBCAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Cipher\\AES256CBCAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Cipher/AES256CBCAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Cipher\\AESCBCAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Cipher/AESCBCAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Cipher\\BlockCipherAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Cipher/BlockCipherAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Cipher\\CipherAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Cipher/CipherAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Cipher\\DESCBCAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Cipher/DESCBCAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Cipher\\DESEDE3CBCAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Cipher/DESEDE3CBCAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Cipher\\RC2CBCAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Cipher/RC2CBCAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Feature\\AlgorithmIdentifierType' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Feature/AlgorithmIdentifierType.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Feature\\AsymmetricCryptoAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Feature/AsymmetricCryptoAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Feature\\EncryptionAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Feature/EncryptionAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Feature\\HashAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Feature/HashAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Feature\\PRFAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Feature/PRFAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Feature\\SignatureAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Feature/SignatureAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\GenericAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/GenericAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Hash\\HMACWithSHA1AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Hash/HMACWithSHA1AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Hash\\HMACWithSHA224AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Hash/HMACWithSHA224AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Hash\\HMACWithSHA256AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Hash/HMACWithSHA256AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Hash\\HMACWithSHA384AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Hash/HMACWithSHA384AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Hash\\HMACWithSHA512AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Hash/HMACWithSHA512AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Hash\\MD5AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Hash/MD5AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Hash\\RFC4231HMACAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Hash/RFC4231HMACAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Hash\\SHA1AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Hash/SHA1AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Hash\\SHA224AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Hash/SHA224AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Hash\\SHA256AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Hash/SHA256AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Hash\\SHA2AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Hash/SHA2AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Hash\\SHA384AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Hash/SHA384AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Hash\\SHA512AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Hash/SHA512AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\ECDSAWithSHA1AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/ECDSAWithSHA1AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\ECDSAWithSHA224AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/ECDSAWithSHA224AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\ECDSAWithSHA256AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/ECDSAWithSHA256AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\ECDSAWithSHA384AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/ECDSAWithSHA384AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\ECDSAWithSHA512AlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/ECDSAWithSHA512AlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\ECSignatureAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/ECSignatureAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\MD2WithRSAEncryptionAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/MD2WithRSAEncryptionAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\MD4WithRSAEncryptionAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/MD4WithRSAEncryptionAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\MD5WithRSAEncryptionAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/MD5WithRSAEncryptionAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\RFC3279RSASignatureAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/RFC3279RSASignatureAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\RFC4055RSASignatureAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/RFC4055RSASignatureAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\RSASignatureAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/RSASignatureAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\SHA1WithRSAEncryptionAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/SHA1WithRSAEncryptionAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\SHA224WithRSAEncryptionAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/SHA224WithRSAEncryptionAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\SHA256WithRSAEncryptionAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/SHA256WithRSAEncryptionAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\SHA384WithRSAEncryptionAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/SHA384WithRSAEncryptionAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\Signature\\SHA512WithRSAEncryptionAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/Signature/SHA512WithRSAEncryptionAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\AlgorithmIdentifier\\SpecificAlgorithmIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/AlgorithmIdentifier/SpecificAlgorithmIdentifier.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\Attribute\\OneAsymmetricKeyAttributes' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/Attribute/OneAsymmetricKeyAttributes.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\EC\\ECConversion' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/EC/ECConversion.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\EC\\ECPrivateKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/EC/ECPrivateKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\EC\\ECPublicKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/EC/ECPublicKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\OneAsymmetricKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/OneAsymmetricKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\PrivateKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/PrivateKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\PrivateKeyInfo' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/PrivateKeyInfo.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\PublicKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/PublicKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\PublicKeyInfo' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/PublicKeyInfo.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RFC8410\\Curve25519\\Curve25519PrivateKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RFC8410/Curve25519/Curve25519PrivateKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RFC8410\\Curve25519\\Curve25519PublicKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RFC8410/Curve25519/Curve25519PublicKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RFC8410\\Curve25519\\Ed25519PrivateKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RFC8410/Curve25519/Ed25519PrivateKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RFC8410\\Curve25519\\Ed25519PublicKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RFC8410/Curve25519/Ed25519PublicKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RFC8410\\Curve25519\\X25519PrivateKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RFC8410/Curve25519/X25519PrivateKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RFC8410\\Curve25519\\X25519PublicKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RFC8410/Curve25519/X25519PublicKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RFC8410\\Curve448\\Ed448PrivateKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RFC8410/Curve448/Ed448PrivateKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RFC8410\\Curve448\\Ed448PublicKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RFC8410/Curve448/Ed448PublicKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RFC8410\\Curve448\\X448PrivateKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RFC8410/Curve448/X448PrivateKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RFC8410\\Curve448\\X448PublicKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RFC8410/Curve448/X448PublicKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RFC8410\\RFC8410PrivateKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RFC8410/RFC8410PrivateKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RFC8410\\RFC8410PublicKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RFC8410/RFC8410PublicKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RSA\\RSAPrivateKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RSA/RSAPrivateKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RSA\\RSAPublicKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RSA/RSAPublicKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Asymmetric\\RSA\\RSASSAPSSPrivateKey' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Asymmetric/RSA/RSASSAPSSPrivateKey.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Signature\\ECSignature' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Signature/ECSignature.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Signature\\Ed25519Signature' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Signature/Ed25519Signature.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Signature\\Ed448Signature' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Signature/Ed448Signature.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Signature\\GenericSignature' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Signature/GenericSignature.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Signature\\RSASignature' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Signature/RSASignature.php',
    'SpomkyLabs\\Pki\\CryptoTypes\\Signature\\Signature' => $vendorDir . '/spomky-labs/pki-framework/src/CryptoTypes/Signature/Signature.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\Attribute' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/Attribute.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeType' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeType.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeTypeAndValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeTypeAndValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\AttributeValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/AttributeValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\CommonNameValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/CommonNameValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\CountryNameValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/CountryNameValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\DescriptionValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/DescriptionValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\Feature\\DirectoryString' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/Feature/DirectoryString.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\Feature\\PrintableStringValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/Feature/PrintableStringValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\GivenNameValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/GivenNameValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\LocalityNameValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/LocalityNameValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\NameValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/NameValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\OrganizationNameValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/OrganizationNameValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\OrganizationalUnitNameValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/OrganizationalUnitNameValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\PseudonymValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/PseudonymValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\SerialNumberValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/SerialNumberValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\StateOrProvinceNameValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/StateOrProvinceNameValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\SurnameValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/SurnameValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\TitleValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/TitleValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\AttributeValue\\UnknownAttributeValue' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/AttributeValue/UnknownAttributeValue.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\Collection\\AttributeCollection' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/Collection/AttributeCollection.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\Collection\\SequenceOfAttributes' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/Collection/SequenceOfAttributes.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\Collection\\SetOfAttributes' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/Collection/SetOfAttributes.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\Name' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/Name.php',
    'SpomkyLabs\\Pki\\X501\\ASN1\\RDN' => $vendorDir . '/spomky-labs/pki-framework/src/X501/ASN1/RDN.php',
    'SpomkyLabs\\Pki\\X501\\DN\\DNParser' => $vendorDir . '/spomky-labs/pki-framework/src/X501/DN/DNParser.php',
    'SpomkyLabs\\Pki\\X501\\MatchingRule\\BinaryMatch' => $vendorDir . '/spomky-labs/pki-framework/src/X501/MatchingRule/BinaryMatch.php',
    'SpomkyLabs\\Pki\\X501\\MatchingRule\\CaseExactMatch' => $vendorDir . '/spomky-labs/pki-framework/src/X501/MatchingRule/CaseExactMatch.php',
    'SpomkyLabs\\Pki\\X501\\MatchingRule\\CaseIgnoreMatch' => $vendorDir . '/spomky-labs/pki-framework/src/X501/MatchingRule/CaseIgnoreMatch.php',
    'SpomkyLabs\\Pki\\X501\\MatchingRule\\MatchingRule' => $vendorDir . '/spomky-labs/pki-framework/src/X501/MatchingRule/MatchingRule.php',
    'SpomkyLabs\\Pki\\X501\\MatchingRule\\StringPrepMatchingRule' => $vendorDir . '/spomky-labs/pki-framework/src/X501/MatchingRule/StringPrepMatchingRule.php',
    'SpomkyLabs\\Pki\\X501\\StringPrep\\CheckBidiStep' => $vendorDir . '/spomky-labs/pki-framework/src/X501/StringPrep/CheckBidiStep.php',
    'SpomkyLabs\\Pki\\X501\\StringPrep\\InsignificantNonSubstringSpaceStep' => $vendorDir . '/spomky-labs/pki-framework/src/X501/StringPrep/InsignificantNonSubstringSpaceStep.php',
    'SpomkyLabs\\Pki\\X501\\StringPrep\\MapStep' => $vendorDir . '/spomky-labs/pki-framework/src/X501/StringPrep/MapStep.php',
    'SpomkyLabs\\Pki\\X501\\StringPrep\\NormalizeStep' => $vendorDir . '/spomky-labs/pki-framework/src/X501/StringPrep/NormalizeStep.php',
    'SpomkyLabs\\Pki\\X501\\StringPrep\\PrepareStep' => $vendorDir . '/spomky-labs/pki-framework/src/X501/StringPrep/PrepareStep.php',
    'SpomkyLabs\\Pki\\X501\\StringPrep\\ProhibitStep' => $vendorDir . '/spomky-labs/pki-framework/src/X501/StringPrep/ProhibitStep.php',
    'SpomkyLabs\\Pki\\X501\\StringPrep\\StringPreparer' => $vendorDir . '/spomky-labs/pki-framework/src/X501/StringPrep/StringPreparer.php',
    'SpomkyLabs\\Pki\\X501\\StringPrep\\TranscodeStep' => $vendorDir . '/spomky-labs/pki-framework/src/X501/StringPrep/TranscodeStep.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\AttCertIssuer' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/AttCertIssuer.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\AttCertValidityPeriod' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/AttCertValidityPeriod.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\AttributeCertificate' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/AttributeCertificate.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\AttributeCertificateInfo' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/AttributeCertificateInfo.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\Attribute\\AccessIdentityAttributeValue' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/Attribute/AccessIdentityAttributeValue.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\Attribute\\AuthenticationInfoAttributeValue' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/Attribute/AuthenticationInfoAttributeValue.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\Attribute\\ChargingIdentityAttributeValue' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/Attribute/ChargingIdentityAttributeValue.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\Attribute\\GroupAttributeValue' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/Attribute/GroupAttributeValue.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\Attribute\\IetfAttrSyntax' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/Attribute/IetfAttrSyntax.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\Attribute\\IetfAttrValue' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/Attribute/IetfAttrValue.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\Attribute\\RoleAttributeValue' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/Attribute/RoleAttributeValue.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\Attribute\\SvceAuthInfo' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/Attribute/SvceAuthInfo.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\Attributes' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/Attributes.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\Holder' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/Holder.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\IssuerSerial' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/IssuerSerial.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\ObjectDigestInfo' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/ObjectDigestInfo.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\V2Form' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/V2Form.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\Validation\\ACValidationConfig' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/Validation/ACValidationConfig.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\Validation\\ACValidator' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/Validation/ACValidator.php',
    'SpomkyLabs\\Pki\\X509\\AttributeCertificate\\Validation\\Exception\\ACValidationException' => $vendorDir . '/spomky-labs/pki-framework/src/X509/AttributeCertificate/Validation/Exception/ACValidationException.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Certificate' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Certificate.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\CertificateBundle' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/CertificateBundle.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\CertificateChain' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/CertificateChain.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\AAControlsExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/AAControlsExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\AccessDescription\\AccessDescription' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/AccessDescription/AccessDescription.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\AccessDescription\\AuthorityAccessDescription' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/AccessDescription/AuthorityAccessDescription.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\AccessDescription\\SubjectAccessDescription' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/AccessDescription/SubjectAccessDescription.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\AuthorityInformationAccessExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/AuthorityInformationAccessExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\AuthorityKeyIdentifierExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/AuthorityKeyIdentifierExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\BasicConstraintsExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/BasicConstraintsExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\CRLDistributionPointsExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/CRLDistributionPointsExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\CertificatePoliciesExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/CertificatePoliciesExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\CertificatePolicy\\CPSQualifier' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/CertificatePolicy/CPSQualifier.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\CertificatePolicy\\DisplayText' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/CertificatePolicy/DisplayText.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\CertificatePolicy\\NoticeReference' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/CertificatePolicy/NoticeReference.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\CertificatePolicy\\PolicyInformation' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/CertificatePolicy/PolicyInformation.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\CertificatePolicy\\PolicyQualifierInfo' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/CertificatePolicy/PolicyQualifierInfo.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\CertificatePolicy\\UserNoticeQualifier' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/CertificatePolicy/UserNoticeQualifier.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\DistributionPoint\\DistributionPoint' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/DistributionPoint/DistributionPoint.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\DistributionPoint\\DistributionPointName' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/DistributionPoint/DistributionPointName.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\DistributionPoint\\FullName' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/DistributionPoint/FullName.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\DistributionPoint\\ReasonFlags' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/DistributionPoint/ReasonFlags.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\DistributionPoint\\RelativeName' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/DistributionPoint/RelativeName.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\ExtendedKeyUsageExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/ExtendedKeyUsageExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\Extension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/Extension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\FreshestCRLExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/FreshestCRLExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\InhibitAnyPolicyExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/InhibitAnyPolicyExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\IssuerAlternativeNameExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/IssuerAlternativeNameExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\KeyUsageExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/KeyUsageExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\NameConstraintsExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/NameConstraintsExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\NameConstraints\\GeneralSubtree' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/NameConstraints/GeneralSubtree.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\NameConstraints\\GeneralSubtrees' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/NameConstraints/GeneralSubtrees.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\NoRevocationAvailableExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/NoRevocationAvailableExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\PolicyConstraintsExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/PolicyConstraintsExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\PolicyMappingsExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/PolicyMappingsExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\PolicyMappings\\PolicyMapping' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/PolicyMappings/PolicyMapping.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\SubjectAlternativeNameExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/SubjectAlternativeNameExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\SubjectDirectoryAttributesExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/SubjectDirectoryAttributesExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\SubjectInformationAccessExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/SubjectInformationAccessExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\SubjectKeyIdentifierExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/SubjectKeyIdentifierExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\TargetInformationExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/TargetInformationExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\Target\\Target' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/Target/Target.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\Target\\TargetGroup' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/Target/TargetGroup.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\Target\\TargetName' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/Target/TargetName.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\Target\\Targets' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/Target/Targets.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extension\\UnknownExtension' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extension/UnknownExtension.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Extensions' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Extensions.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\TBSCertificate' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/TBSCertificate.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Time' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Time.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\UniqueIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/UniqueIdentifier.php',
    'SpomkyLabs\\Pki\\X509\\Certificate\\Validity' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Certificate/Validity.php',
    'SpomkyLabs\\Pki\\X509\\CertificationPath\\CertificationPath' => $vendorDir . '/spomky-labs/pki-framework/src/X509/CertificationPath/CertificationPath.php',
    'SpomkyLabs\\Pki\\X509\\CertificationPath\\Exception\\PathBuildingException' => $vendorDir . '/spomky-labs/pki-framework/src/X509/CertificationPath/Exception/PathBuildingException.php',
    'SpomkyLabs\\Pki\\X509\\CertificationPath\\Exception\\PathValidationException' => $vendorDir . '/spomky-labs/pki-framework/src/X509/CertificationPath/Exception/PathValidationException.php',
    'SpomkyLabs\\Pki\\X509\\CertificationPath\\PathBuilding\\CertificationPathBuilder' => $vendorDir . '/spomky-labs/pki-framework/src/X509/CertificationPath/PathBuilding/CertificationPathBuilder.php',
    'SpomkyLabs\\Pki\\X509\\CertificationPath\\PathValidation\\PathValidationConfig' => $vendorDir . '/spomky-labs/pki-framework/src/X509/CertificationPath/PathValidation/PathValidationConfig.php',
    'SpomkyLabs\\Pki\\X509\\CertificationPath\\PathValidation\\PathValidationResult' => $vendorDir . '/spomky-labs/pki-framework/src/X509/CertificationPath/PathValidation/PathValidationResult.php',
    'SpomkyLabs\\Pki\\X509\\CertificationPath\\PathValidation\\PathValidator' => $vendorDir . '/spomky-labs/pki-framework/src/X509/CertificationPath/PathValidation/PathValidator.php',
    'SpomkyLabs\\Pki\\X509\\CertificationPath\\PathValidation\\ValidatorState' => $vendorDir . '/spomky-labs/pki-framework/src/X509/CertificationPath/PathValidation/ValidatorState.php',
    'SpomkyLabs\\Pki\\X509\\CertificationPath\\Policy\\PolicyNode' => $vendorDir . '/spomky-labs/pki-framework/src/X509/CertificationPath/Policy/PolicyNode.php',
    'SpomkyLabs\\Pki\\X509\\CertificationPath\\Policy\\PolicyTree' => $vendorDir . '/spomky-labs/pki-framework/src/X509/CertificationPath/Policy/PolicyTree.php',
    'SpomkyLabs\\Pki\\X509\\CertificationRequest\\Attribute\\ExtensionRequestValue' => $vendorDir . '/spomky-labs/pki-framework/src/X509/CertificationRequest/Attribute/ExtensionRequestValue.php',
    'SpomkyLabs\\Pki\\X509\\CertificationRequest\\Attributes' => $vendorDir . '/spomky-labs/pki-framework/src/X509/CertificationRequest/Attributes.php',
    'SpomkyLabs\\Pki\\X509\\CertificationRequest\\CertificationRequest' => $vendorDir . '/spomky-labs/pki-framework/src/X509/CertificationRequest/CertificationRequest.php',
    'SpomkyLabs\\Pki\\X509\\CertificationRequest\\CertificationRequestInfo' => $vendorDir . '/spomky-labs/pki-framework/src/X509/CertificationRequest/CertificationRequestInfo.php',
    'SpomkyLabs\\Pki\\X509\\Exception\\X509ValidationException' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Exception/X509ValidationException.php',
    'SpomkyLabs\\Pki\\X509\\Feature\\DateTimeHelper' => $vendorDir . '/spomky-labs/pki-framework/src/X509/Feature/DateTimeHelper.php',
    'SpomkyLabs\\Pki\\X509\\GeneralName\\DNSName' => $vendorDir . '/spomky-labs/pki-framework/src/X509/GeneralName/DNSName.php',
    'SpomkyLabs\\Pki\\X509\\GeneralName\\DirectoryName' => $vendorDir . '/spomky-labs/pki-framework/src/X509/GeneralName/DirectoryName.php',
    'SpomkyLabs\\Pki\\X509\\GeneralName\\EDIPartyName' => $vendorDir . '/spomky-labs/pki-framework/src/X509/GeneralName/EDIPartyName.php',
    'SpomkyLabs\\Pki\\X509\\GeneralName\\GeneralName' => $vendorDir . '/spomky-labs/pki-framework/src/X509/GeneralName/GeneralName.php',
    'SpomkyLabs\\Pki\\X509\\GeneralName\\GeneralNames' => $vendorDir . '/spomky-labs/pki-framework/src/X509/GeneralName/GeneralNames.php',
    'SpomkyLabs\\Pki\\X509\\GeneralName\\IPAddress' => $vendorDir . '/spomky-labs/pki-framework/src/X509/GeneralName/IPAddress.php',
    'SpomkyLabs\\Pki\\X509\\GeneralName\\IPv4Address' => $vendorDir . '/spomky-labs/pki-framework/src/X509/GeneralName/IPv4Address.php',
    'SpomkyLabs\\Pki\\X509\\GeneralName\\IPv6Address' => $vendorDir . '/spomky-labs/pki-framework/src/X509/GeneralName/IPv6Address.php',
    'SpomkyLabs\\Pki\\X509\\GeneralName\\OtherName' => $vendorDir . '/spomky-labs/pki-framework/src/X509/GeneralName/OtherName.php',
    'SpomkyLabs\\Pki\\X509\\GeneralName\\RFC822Name' => $vendorDir . '/spomky-labs/pki-framework/src/X509/GeneralName/RFC822Name.php',
    'SpomkyLabs\\Pki\\X509\\GeneralName\\RegisteredID' => $vendorDir . '/spomky-labs/pki-framework/src/X509/GeneralName/RegisteredID.php',
    'SpomkyLabs\\Pki\\X509\\GeneralName\\UniformResourceIdentifier' => $vendorDir . '/spomky-labs/pki-framework/src/X509/GeneralName/UniformResourceIdentifier.php',
    'SpomkyLabs\\Pki\\X509\\GeneralName\\X400Address' => $vendorDir . '/spomky-labs/pki-framework/src/X509/GeneralName/X400Address.php',
    'Symfony\\Component\\Console\\Application' => $vendorDir . '/symfony/console/Application.php',
    'Symfony\\Component\\Console\\Attribute\\Argument' => $vendorDir . '/symfony/console/Attribute/Argument.php',
    'Symfony\\Component\\Console\\Attribute\\AsCommand' => $vendorDir . '/symfony/console/Attribute/AsCommand.php',
    'Symfony\\Component\\Console\\Attribute\\Option' => $vendorDir . '/symfony/console/Attribute/Option.php',
    'Symfony\\Component\\Console\\CI\\GithubActionReporter' => $vendorDir . '/symfony/console/CI/GithubActionReporter.php',
    'Symfony\\Component\\Console\\Color' => $vendorDir . '/symfony/console/Color.php',
    'Symfony\\Component\\Console\\CommandLoader\\CommandLoaderInterface' => $vendorDir . '/symfony/console/CommandLoader/CommandLoaderInterface.php',
    'Symfony\\Component\\Console\\CommandLoader\\ContainerCommandLoader' => $vendorDir . '/symfony/console/CommandLoader/ContainerCommandLoader.php',
    'Symfony\\Component\\Console\\CommandLoader\\FactoryCommandLoader' => $vendorDir . '/symfony/console/CommandLoader/FactoryCommandLoader.php',
    'Symfony\\Component\\Console\\Command\\Command' => $vendorDir . '/symfony/console/Command/Command.php',
    'Symfony\\Component\\Console\\Command\\CompleteCommand' => $vendorDir . '/symfony/console/Command/CompleteCommand.php',
    'Symfony\\Component\\Console\\Command\\DumpCompletionCommand' => $vendorDir . '/symfony/console/Command/DumpCompletionCommand.php',
    'Symfony\\Component\\Console\\Command\\HelpCommand' => $vendorDir . '/symfony/console/Command/HelpCommand.php',
    'Symfony\\Component\\Console\\Command\\InvokableCommand' => $vendorDir . '/symfony/console/Command/InvokableCommand.php',
    'Symfony\\Component\\Console\\Command\\LazyCommand' => $vendorDir . '/symfony/console/Command/LazyCommand.php',
    'Symfony\\Component\\Console\\Command\\ListCommand' => $vendorDir . '/symfony/console/Command/ListCommand.php',
    'Symfony\\Component\\Console\\Command\\LockableTrait' => $vendorDir . '/symfony/console/Command/LockableTrait.php',
    'Symfony\\Component\\Console\\Command\\SignalableCommandInterface' => $vendorDir . '/symfony/console/Command/SignalableCommandInterface.php',
    'Symfony\\Component\\Console\\Command\\TraceableCommand' => $vendorDir . '/symfony/console/Command/TraceableCommand.php',
    'Symfony\\Component\\Console\\Completion\\CompletionInput' => $vendorDir . '/symfony/console/Completion/CompletionInput.php',
    'Symfony\\Component\\Console\\Completion\\CompletionSuggestions' => $vendorDir . '/symfony/console/Completion/CompletionSuggestions.php',
    'Symfony\\Component\\Console\\Completion\\Output\\BashCompletionOutput' => $vendorDir . '/symfony/console/Completion/Output/BashCompletionOutput.php',
    'Symfony\\Component\\Console\\Completion\\Output\\CompletionOutputInterface' => $vendorDir . '/symfony/console/Completion/Output/CompletionOutputInterface.php',
    'Symfony\\Component\\Console\\Completion\\Output\\FishCompletionOutput' => $vendorDir . '/symfony/console/Completion/Output/FishCompletionOutput.php',
    'Symfony\\Component\\Console\\Completion\\Output\\ZshCompletionOutput' => $vendorDir . '/symfony/console/Completion/Output/ZshCompletionOutput.php',
    'Symfony\\Component\\Console\\Completion\\Suggestion' => $vendorDir . '/symfony/console/Completion/Suggestion.php',
    'Symfony\\Component\\Console\\ConsoleEvents' => $vendorDir . '/symfony/console/ConsoleEvents.php',
    'Symfony\\Component\\Console\\Cursor' => $vendorDir . '/symfony/console/Cursor.php',
    'Symfony\\Component\\Console\\DataCollector\\CommandDataCollector' => $vendorDir . '/symfony/console/DataCollector/CommandDataCollector.php',
    'Symfony\\Component\\Console\\Debug\\CliRequest' => $vendorDir . '/symfony/console/Debug/CliRequest.php',
    'Symfony\\Component\\Console\\DependencyInjection\\AddConsoleCommandPass' => $vendorDir . '/symfony/console/DependencyInjection/AddConsoleCommandPass.php',
    'Symfony\\Component\\Console\\Descriptor\\ApplicationDescription' => $vendorDir . '/symfony/console/Descriptor/ApplicationDescription.php',
    'Symfony\\Component\\Console\\Descriptor\\Descriptor' => $vendorDir . '/symfony/console/Descriptor/Descriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\DescriptorInterface' => $vendorDir . '/symfony/console/Descriptor/DescriptorInterface.php',
    'Symfony\\Component\\Console\\Descriptor\\JsonDescriptor' => $vendorDir . '/symfony/console/Descriptor/JsonDescriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\MarkdownDescriptor' => $vendorDir . '/symfony/console/Descriptor/MarkdownDescriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\ReStructuredTextDescriptor' => $vendorDir . '/symfony/console/Descriptor/ReStructuredTextDescriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\TextDescriptor' => $vendorDir . '/symfony/console/Descriptor/TextDescriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\XmlDescriptor' => $vendorDir . '/symfony/console/Descriptor/XmlDescriptor.php',
    'Symfony\\Component\\Console\\EventListener\\ErrorListener' => $vendorDir . '/symfony/console/EventListener/ErrorListener.php',
    'Symfony\\Component\\Console\\Event\\ConsoleAlarmEvent' => $vendorDir . '/symfony/console/Event/ConsoleAlarmEvent.php',
    'Symfony\\Component\\Console\\Event\\ConsoleCommandEvent' => $vendorDir . '/symfony/console/Event/ConsoleCommandEvent.php',
    'Symfony\\Component\\Console\\Event\\ConsoleErrorEvent' => $vendorDir . '/symfony/console/Event/ConsoleErrorEvent.php',
    'Symfony\\Component\\Console\\Event\\ConsoleEvent' => $vendorDir . '/symfony/console/Event/ConsoleEvent.php',
    'Symfony\\Component\\Console\\Event\\ConsoleSignalEvent' => $vendorDir . '/symfony/console/Event/ConsoleSignalEvent.php',
    'Symfony\\Component\\Console\\Event\\ConsoleTerminateEvent' => $vendorDir . '/symfony/console/Event/ConsoleTerminateEvent.php',
    'Symfony\\Component\\Console\\Exception\\CommandNotFoundException' => $vendorDir . '/symfony/console/Exception/CommandNotFoundException.php',
    'Symfony\\Component\\Console\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/console/Exception/ExceptionInterface.php',
    'Symfony\\Component\\Console\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/console/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\Console\\Exception\\InvalidOptionException' => $vendorDir . '/symfony/console/Exception/InvalidOptionException.php',
    'Symfony\\Component\\Console\\Exception\\LogicException' => $vendorDir . '/symfony/console/Exception/LogicException.php',
    'Symfony\\Component\\Console\\Exception\\MissingInputException' => $vendorDir . '/symfony/console/Exception/MissingInputException.php',
    'Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException' => $vendorDir . '/symfony/console/Exception/NamespaceNotFoundException.php',
    'Symfony\\Component\\Console\\Exception\\RunCommandFailedException' => $vendorDir . '/symfony/console/Exception/RunCommandFailedException.php',
    'Symfony\\Component\\Console\\Exception\\RuntimeException' => $vendorDir . '/symfony/console/Exception/RuntimeException.php',
    'Symfony\\Component\\Console\\Formatter\\NullOutputFormatter' => $vendorDir . '/symfony/console/Formatter/NullOutputFormatter.php',
    'Symfony\\Component\\Console\\Formatter\\NullOutputFormatterStyle' => $vendorDir . '/symfony/console/Formatter/NullOutputFormatterStyle.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatter' => $vendorDir . '/symfony/console/Formatter/OutputFormatter.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatterInterface' => $vendorDir . '/symfony/console/Formatter/OutputFormatterInterface.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatterStyle' => $vendorDir . '/symfony/console/Formatter/OutputFormatterStyle.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatterStyleInterface' => $vendorDir . '/symfony/console/Formatter/OutputFormatterStyleInterface.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatterStyleStack' => $vendorDir . '/symfony/console/Formatter/OutputFormatterStyleStack.php',
    'Symfony\\Component\\Console\\Formatter\\WrappableOutputFormatterInterface' => $vendorDir . '/symfony/console/Formatter/WrappableOutputFormatterInterface.php',
    'Symfony\\Component\\Console\\Helper\\DebugFormatterHelper' => $vendorDir . '/symfony/console/Helper/DebugFormatterHelper.php',
    'Symfony\\Component\\Console\\Helper\\DescriptorHelper' => $vendorDir . '/symfony/console/Helper/DescriptorHelper.php',
    'Symfony\\Component\\Console\\Helper\\Dumper' => $vendorDir . '/symfony/console/Helper/Dumper.php',
    'Symfony\\Component\\Console\\Helper\\FormatterHelper' => $vendorDir . '/symfony/console/Helper/FormatterHelper.php',
    'Symfony\\Component\\Console\\Helper\\Helper' => $vendorDir . '/symfony/console/Helper/Helper.php',
    'Symfony\\Component\\Console\\Helper\\HelperInterface' => $vendorDir . '/symfony/console/Helper/HelperInterface.php',
    'Symfony\\Component\\Console\\Helper\\HelperSet' => $vendorDir . '/symfony/console/Helper/HelperSet.php',
    'Symfony\\Component\\Console\\Helper\\InputAwareHelper' => $vendorDir . '/symfony/console/Helper/InputAwareHelper.php',
    'Symfony\\Component\\Console\\Helper\\OutputWrapper' => $vendorDir . '/symfony/console/Helper/OutputWrapper.php',
    'Symfony\\Component\\Console\\Helper\\ProcessHelper' => $vendorDir . '/symfony/console/Helper/ProcessHelper.php',
    'Symfony\\Component\\Console\\Helper\\ProgressBar' => $vendorDir . '/symfony/console/Helper/ProgressBar.php',
    'Symfony\\Component\\Console\\Helper\\ProgressIndicator' => $vendorDir . '/symfony/console/Helper/ProgressIndicator.php',
    'Symfony\\Component\\Console\\Helper\\QuestionHelper' => $vendorDir . '/symfony/console/Helper/QuestionHelper.php',
    'Symfony\\Component\\Console\\Helper\\SymfonyQuestionHelper' => $vendorDir . '/symfony/console/Helper/SymfonyQuestionHelper.php',
    'Symfony\\Component\\Console\\Helper\\Table' => $vendorDir . '/symfony/console/Helper/Table.php',
    'Symfony\\Component\\Console\\Helper\\TableCell' => $vendorDir . '/symfony/console/Helper/TableCell.php',
    'Symfony\\Component\\Console\\Helper\\TableCellStyle' => $vendorDir . '/symfony/console/Helper/TableCellStyle.php',
    'Symfony\\Component\\Console\\Helper\\TableRows' => $vendorDir . '/symfony/console/Helper/TableRows.php',
    'Symfony\\Component\\Console\\Helper\\TableSeparator' => $vendorDir . '/symfony/console/Helper/TableSeparator.php',
    'Symfony\\Component\\Console\\Helper\\TableStyle' => $vendorDir . '/symfony/console/Helper/TableStyle.php',
    'Symfony\\Component\\Console\\Helper\\TreeHelper' => $vendorDir . '/symfony/console/Helper/TreeHelper.php',
    'Symfony\\Component\\Console\\Helper\\TreeNode' => $vendorDir . '/symfony/console/Helper/TreeNode.php',
    'Symfony\\Component\\Console\\Helper\\TreeStyle' => $vendorDir . '/symfony/console/Helper/TreeStyle.php',
    'Symfony\\Component\\Console\\Input\\ArgvInput' => $vendorDir . '/symfony/console/Input/ArgvInput.php',
    'Symfony\\Component\\Console\\Input\\ArrayInput' => $vendorDir . '/symfony/console/Input/ArrayInput.php',
    'Symfony\\Component\\Console\\Input\\Input' => $vendorDir . '/symfony/console/Input/Input.php',
    'Symfony\\Component\\Console\\Input\\InputArgument' => $vendorDir . '/symfony/console/Input/InputArgument.php',
    'Symfony\\Component\\Console\\Input\\InputAwareInterface' => $vendorDir . '/symfony/console/Input/InputAwareInterface.php',
    'Symfony\\Component\\Console\\Input\\InputDefinition' => $vendorDir . '/symfony/console/Input/InputDefinition.php',
    'Symfony\\Component\\Console\\Input\\InputInterface' => $vendorDir . '/symfony/console/Input/InputInterface.php',
    'Symfony\\Component\\Console\\Input\\InputOption' => $vendorDir . '/symfony/console/Input/InputOption.php',
    'Symfony\\Component\\Console\\Input\\StreamableInputInterface' => $vendorDir . '/symfony/console/Input/StreamableInputInterface.php',
    'Symfony\\Component\\Console\\Input\\StringInput' => $vendorDir . '/symfony/console/Input/StringInput.php',
    'Symfony\\Component\\Console\\Logger\\ConsoleLogger' => $vendorDir . '/symfony/console/Logger/ConsoleLogger.php',
    'Symfony\\Component\\Console\\Messenger\\RunCommandContext' => $vendorDir . '/symfony/console/Messenger/RunCommandContext.php',
    'Symfony\\Component\\Console\\Messenger\\RunCommandMessage' => $vendorDir . '/symfony/console/Messenger/RunCommandMessage.php',
    'Symfony\\Component\\Console\\Messenger\\RunCommandMessageHandler' => $vendorDir . '/symfony/console/Messenger/RunCommandMessageHandler.php',
    'Symfony\\Component\\Console\\Output\\AnsiColorMode' => $vendorDir . '/symfony/console/Output/AnsiColorMode.php',
    'Symfony\\Component\\Console\\Output\\BufferedOutput' => $vendorDir . '/symfony/console/Output/BufferedOutput.php',
    'Symfony\\Component\\Console\\Output\\ConsoleOutput' => $vendorDir . '/symfony/console/Output/ConsoleOutput.php',
    'Symfony\\Component\\Console\\Output\\ConsoleOutputInterface' => $vendorDir . '/symfony/console/Output/ConsoleOutputInterface.php',
    'Symfony\\Component\\Console\\Output\\ConsoleSectionOutput' => $vendorDir . '/symfony/console/Output/ConsoleSectionOutput.php',
    'Symfony\\Component\\Console\\Output\\NullOutput' => $vendorDir . '/symfony/console/Output/NullOutput.php',
    'Symfony\\Component\\Console\\Output\\Output' => $vendorDir . '/symfony/console/Output/Output.php',
    'Symfony\\Component\\Console\\Output\\OutputInterface' => $vendorDir . '/symfony/console/Output/OutputInterface.php',
    'Symfony\\Component\\Console\\Output\\StreamOutput' => $vendorDir . '/symfony/console/Output/StreamOutput.php',
    'Symfony\\Component\\Console\\Output\\TrimmedBufferOutput' => $vendorDir . '/symfony/console/Output/TrimmedBufferOutput.php',
    'Symfony\\Component\\Console\\Question\\ChoiceQuestion' => $vendorDir . '/symfony/console/Question/ChoiceQuestion.php',
    'Symfony\\Component\\Console\\Question\\ConfirmationQuestion' => $vendorDir . '/symfony/console/Question/ConfirmationQuestion.php',
    'Symfony\\Component\\Console\\Question\\Question' => $vendorDir . '/symfony/console/Question/Question.php',
    'Symfony\\Component\\Console\\SignalRegistry\\SignalMap' => $vendorDir . '/symfony/console/SignalRegistry/SignalMap.php',
    'Symfony\\Component\\Console\\SignalRegistry\\SignalRegistry' => $vendorDir . '/symfony/console/SignalRegistry/SignalRegistry.php',
    'Symfony\\Component\\Console\\SingleCommandApplication' => $vendorDir . '/symfony/console/SingleCommandApplication.php',
    'Symfony\\Component\\Console\\Style\\OutputStyle' => $vendorDir . '/symfony/console/Style/OutputStyle.php',
    'Symfony\\Component\\Console\\Style\\StyleInterface' => $vendorDir . '/symfony/console/Style/StyleInterface.php',
    'Symfony\\Component\\Console\\Style\\SymfonyStyle' => $vendorDir . '/symfony/console/Style/SymfonyStyle.php',
    'Symfony\\Component\\Console\\Terminal' => $vendorDir . '/symfony/console/Terminal.php',
    'Symfony\\Component\\Console\\Tester\\ApplicationTester' => $vendorDir . '/symfony/console/Tester/ApplicationTester.php',
    'Symfony\\Component\\Console\\Tester\\CommandCompletionTester' => $vendorDir . '/symfony/console/Tester/CommandCompletionTester.php',
    'Symfony\\Component\\Console\\Tester\\CommandTester' => $vendorDir . '/symfony/console/Tester/CommandTester.php',
    'Symfony\\Component\\Console\\Tester\\Constraint\\CommandIsSuccessful' => $vendorDir . '/symfony/console/Tester/Constraint/CommandIsSuccessful.php',
    'Symfony\\Component\\Console\\Tester\\TesterTrait' => $vendorDir . '/symfony/console/Tester/TesterTrait.php',
    'Symfony\\Component\\HttpClient\\AmpHttpClient' => $vendorDir . '/symfony/http-client/AmpHttpClient.php',
    'Symfony\\Component\\HttpClient\\AsyncDecoratorTrait' => $vendorDir . '/symfony/http-client/AsyncDecoratorTrait.php',
    'Symfony\\Component\\HttpClient\\CachingHttpClient' => $vendorDir . '/symfony/http-client/CachingHttpClient.php',
    'Symfony\\Component\\HttpClient\\Chunk\\DataChunk' => $vendorDir . '/symfony/http-client/Chunk/DataChunk.php',
    'Symfony\\Component\\HttpClient\\Chunk\\ErrorChunk' => $vendorDir . '/symfony/http-client/Chunk/ErrorChunk.php',
    'Symfony\\Component\\HttpClient\\Chunk\\FirstChunk' => $vendorDir . '/symfony/http-client/Chunk/FirstChunk.php',
    'Symfony\\Component\\HttpClient\\Chunk\\InformationalChunk' => $vendorDir . '/symfony/http-client/Chunk/InformationalChunk.php',
    'Symfony\\Component\\HttpClient\\Chunk\\LastChunk' => $vendorDir . '/symfony/http-client/Chunk/LastChunk.php',
    'Symfony\\Component\\HttpClient\\Chunk\\ServerSentEvent' => $vendorDir . '/symfony/http-client/Chunk/ServerSentEvent.php',
    'Symfony\\Component\\HttpClient\\CurlHttpClient' => $vendorDir . '/symfony/http-client/CurlHttpClient.php',
    'Symfony\\Component\\HttpClient\\DataCollector\\HttpClientDataCollector' => $vendorDir . '/symfony/http-client/DataCollector/HttpClientDataCollector.php',
    'Symfony\\Component\\HttpClient\\DecoratorTrait' => $vendorDir . '/symfony/http-client/DecoratorTrait.php',
    'Symfony\\Component\\HttpClient\\DependencyInjection\\HttpClientPass' => $vendorDir . '/symfony/http-client/DependencyInjection/HttpClientPass.php',
    'Symfony\\Component\\HttpClient\\EventSourceHttpClient' => $vendorDir . '/symfony/http-client/EventSourceHttpClient.php',
    'Symfony\\Component\\HttpClient\\Exception\\ClientException' => $vendorDir . '/symfony/http-client/Exception/ClientException.php',
    'Symfony\\Component\\HttpClient\\Exception\\EventSourceException' => $vendorDir . '/symfony/http-client/Exception/EventSourceException.php',
    'Symfony\\Component\\HttpClient\\Exception\\HttpExceptionTrait' => $vendorDir . '/symfony/http-client/Exception/HttpExceptionTrait.php',
    'Symfony\\Component\\HttpClient\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/http-client/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\HttpClient\\Exception\\JsonException' => $vendorDir . '/symfony/http-client/Exception/JsonException.php',
    'Symfony\\Component\\HttpClient\\Exception\\RedirectionException' => $vendorDir . '/symfony/http-client/Exception/RedirectionException.php',
    'Symfony\\Component\\HttpClient\\Exception\\ServerException' => $vendorDir . '/symfony/http-client/Exception/ServerException.php',
    'Symfony\\Component\\HttpClient\\Exception\\TimeoutException' => $vendorDir . '/symfony/http-client/Exception/TimeoutException.php',
    'Symfony\\Component\\HttpClient\\Exception\\TransportException' => $vendorDir . '/symfony/http-client/Exception/TransportException.php',
    'Symfony\\Component\\HttpClient\\HttpClient' => $vendorDir . '/symfony/http-client/HttpClient.php',
    'Symfony\\Component\\HttpClient\\HttpClientTrait' => $vendorDir . '/symfony/http-client/HttpClientTrait.php',
    'Symfony\\Component\\HttpClient\\HttpOptions' => $vendorDir . '/symfony/http-client/HttpOptions.php',
    'Symfony\\Component\\HttpClient\\HttplugClient' => $vendorDir . '/symfony/http-client/HttplugClient.php',
    'Symfony\\Component\\HttpClient\\Internal\\AmpBodyV4' => $vendorDir . '/symfony/http-client/Internal/AmpBodyV4.php',
    'Symfony\\Component\\HttpClient\\Internal\\AmpBodyV5' => $vendorDir . '/symfony/http-client/Internal/AmpBodyV5.php',
    'Symfony\\Component\\HttpClient\\Internal\\AmpClientStateV4' => $vendorDir . '/symfony/http-client/Internal/AmpClientStateV4.php',
    'Symfony\\Component\\HttpClient\\Internal\\AmpClientStateV5' => $vendorDir . '/symfony/http-client/Internal/AmpClientStateV5.php',
    'Symfony\\Component\\HttpClient\\Internal\\AmpListenerV4' => $vendorDir . '/symfony/http-client/Internal/AmpListenerV4.php',
    'Symfony\\Component\\HttpClient\\Internal\\AmpListenerV5' => $vendorDir . '/symfony/http-client/Internal/AmpListenerV5.php',
    'Symfony\\Component\\HttpClient\\Internal\\AmpResolverV4' => $vendorDir . '/symfony/http-client/Internal/AmpResolverV4.php',
    'Symfony\\Component\\HttpClient\\Internal\\AmpResolverV5' => $vendorDir . '/symfony/http-client/Internal/AmpResolverV5.php',
    'Symfony\\Component\\HttpClient\\Internal\\Canary' => $vendorDir . '/symfony/http-client/Internal/Canary.php',
    'Symfony\\Component\\HttpClient\\Internal\\ClientState' => $vendorDir . '/symfony/http-client/Internal/ClientState.php',
    'Symfony\\Component\\HttpClient\\Internal\\CurlClientState' => $vendorDir . '/symfony/http-client/Internal/CurlClientState.php',
    'Symfony\\Component\\HttpClient\\Internal\\DnsCache' => $vendorDir . '/symfony/http-client/Internal/DnsCache.php',
    'Symfony\\Component\\HttpClient\\Internal\\HttplugWaitLoop' => $vendorDir . '/symfony/http-client/Internal/HttplugWaitLoop.php',
    'Symfony\\Component\\HttpClient\\Internal\\NativeClientState' => $vendorDir . '/symfony/http-client/Internal/NativeClientState.php',
    'Symfony\\Component\\HttpClient\\Internal\\PushedResponse' => $vendorDir . '/symfony/http-client/Internal/PushedResponse.php',
    'Symfony\\Component\\HttpClient\\Messenger\\PingWebhookMessage' => $vendorDir . '/symfony/http-client/Messenger/PingWebhookMessage.php',
    'Symfony\\Component\\HttpClient\\Messenger\\PingWebhookMessageHandler' => $vendorDir . '/symfony/http-client/Messenger/PingWebhookMessageHandler.php',
    'Symfony\\Component\\HttpClient\\MockHttpClient' => $vendorDir . '/symfony/http-client/MockHttpClient.php',
    'Symfony\\Component\\HttpClient\\NativeHttpClient' => $vendorDir . '/symfony/http-client/NativeHttpClient.php',
    'Symfony\\Component\\HttpClient\\NoPrivateNetworkHttpClient' => $vendorDir . '/symfony/http-client/NoPrivateNetworkHttpClient.php',
    'Symfony\\Component\\HttpClient\\Psr18Client' => $vendorDir . '/symfony/http-client/Psr18Client.php',
    'Symfony\\Component\\HttpClient\\Response\\AmpResponseV4' => $vendorDir . '/symfony/http-client/Response/AmpResponseV4.php',
    'Symfony\\Component\\HttpClient\\Response\\AmpResponseV5' => $vendorDir . '/symfony/http-client/Response/AmpResponseV5.php',
    'Symfony\\Component\\HttpClient\\Response\\AsyncContext' => $vendorDir . '/symfony/http-client/Response/AsyncContext.php',
    'Symfony\\Component\\HttpClient\\Response\\AsyncResponse' => $vendorDir . '/symfony/http-client/Response/AsyncResponse.php',
    'Symfony\\Component\\HttpClient\\Response\\CommonResponseTrait' => $vendorDir . '/symfony/http-client/Response/CommonResponseTrait.php',
    'Symfony\\Component\\HttpClient\\Response\\CurlResponse' => $vendorDir . '/symfony/http-client/Response/CurlResponse.php',
    'Symfony\\Component\\HttpClient\\Response\\HttplugPromise' => $vendorDir . '/symfony/http-client/Response/HttplugPromise.php',
    'Symfony\\Component\\HttpClient\\Response\\JsonMockResponse' => $vendorDir . '/symfony/http-client/Response/JsonMockResponse.php',
    'Symfony\\Component\\HttpClient\\Response\\MockResponse' => $vendorDir . '/symfony/http-client/Response/MockResponse.php',
    'Symfony\\Component\\HttpClient\\Response\\NativeResponse' => $vendorDir . '/symfony/http-client/Response/NativeResponse.php',
    'Symfony\\Component\\HttpClient\\Response\\ResponseStream' => $vendorDir . '/symfony/http-client/Response/ResponseStream.php',
    'Symfony\\Component\\HttpClient\\Response\\StreamWrapper' => $vendorDir . '/symfony/http-client/Response/StreamWrapper.php',
    'Symfony\\Component\\HttpClient\\Response\\StreamableInterface' => $vendorDir . '/symfony/http-client/Response/StreamableInterface.php',
    'Symfony\\Component\\HttpClient\\Response\\TraceableResponse' => $vendorDir . '/symfony/http-client/Response/TraceableResponse.php',
    'Symfony\\Component\\HttpClient\\Response\\TransportResponseTrait' => $vendorDir . '/symfony/http-client/Response/TransportResponseTrait.php',
    'Symfony\\Component\\HttpClient\\Retry\\GenericRetryStrategy' => $vendorDir . '/symfony/http-client/Retry/GenericRetryStrategy.php',
    'Symfony\\Component\\HttpClient\\Retry\\RetryStrategyInterface' => $vendorDir . '/symfony/http-client/Retry/RetryStrategyInterface.php',
    'Symfony\\Component\\HttpClient\\RetryableHttpClient' => $vendorDir . '/symfony/http-client/RetryableHttpClient.php',
    'Symfony\\Component\\HttpClient\\ScopingHttpClient' => $vendorDir . '/symfony/http-client/ScopingHttpClient.php',
    'Symfony\\Component\\HttpClient\\Test\\HarFileResponseFactory' => $vendorDir . '/symfony/http-client/Test/HarFileResponseFactory.php',
    'Symfony\\Component\\HttpClient\\ThrottlingHttpClient' => $vendorDir . '/symfony/http-client/ThrottlingHttpClient.php',
    'Symfony\\Component\\HttpClient\\TraceableHttpClient' => $vendorDir . '/symfony/http-client/TraceableHttpClient.php',
    'Symfony\\Component\\HttpClient\\UriTemplateHttpClient' => $vendorDir . '/symfony/http-client/UriTemplateHttpClient.php',
    'Symfony\\Component\\String\\AbstractString' => $vendorDir . '/symfony/string/AbstractString.php',
    'Symfony\\Component\\String\\AbstractUnicodeString' => $vendorDir . '/symfony/string/AbstractUnicodeString.php',
    'Symfony\\Component\\String\\ByteString' => $vendorDir . '/symfony/string/ByteString.php',
    'Symfony\\Component\\String\\CodePointString' => $vendorDir . '/symfony/string/CodePointString.php',
    'Symfony\\Component\\String\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/string/Exception/ExceptionInterface.php',
    'Symfony\\Component\\String\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/string/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\String\\Exception\\RuntimeException' => $vendorDir . '/symfony/string/Exception/RuntimeException.php',
    'Symfony\\Component\\String\\Inflector\\EnglishInflector' => $vendorDir . '/symfony/string/Inflector/EnglishInflector.php',
    'Symfony\\Component\\String\\Inflector\\FrenchInflector' => $vendorDir . '/symfony/string/Inflector/FrenchInflector.php',
    'Symfony\\Component\\String\\Inflector\\InflectorInterface' => $vendorDir . '/symfony/string/Inflector/InflectorInterface.php',
    'Symfony\\Component\\String\\Inflector\\SpanishInflector' => $vendorDir . '/symfony/string/Inflector/SpanishInflector.php',
    'Symfony\\Component\\String\\LazyString' => $vendorDir . '/symfony/string/LazyString.php',
    'Symfony\\Component\\String\\Slugger\\AsciiSlugger' => $vendorDir . '/symfony/string/Slugger/AsciiSlugger.php',
    'Symfony\\Component\\String\\Slugger\\SluggerInterface' => $vendorDir . '/symfony/string/Slugger/SluggerInterface.php',
    'Symfony\\Component\\String\\TruncateMode' => $vendorDir . '/symfony/string/TruncateMode.php',
    'Symfony\\Component\\String\\UnicodeString' => $vendorDir . '/symfony/string/UnicodeString.php',
    'Symfony\\Contracts\\HttpClient\\ChunkInterface' => $vendorDir . '/symfony/http-client-contracts/ChunkInterface.php',
    'Symfony\\Contracts\\HttpClient\\Exception\\ClientExceptionInterface' => $vendorDir . '/symfony/http-client-contracts/Exception/ClientExceptionInterface.php',
    'Symfony\\Contracts\\HttpClient\\Exception\\DecodingExceptionInterface' => $vendorDir . '/symfony/http-client-contracts/Exception/DecodingExceptionInterface.php',
    'Symfony\\Contracts\\HttpClient\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/http-client-contracts/Exception/ExceptionInterface.php',
    'Symfony\\Contracts\\HttpClient\\Exception\\HttpExceptionInterface' => $vendorDir . '/symfony/http-client-contracts/Exception/HttpExceptionInterface.php',
    'Symfony\\Contracts\\HttpClient\\Exception\\RedirectionExceptionInterface' => $vendorDir . '/symfony/http-client-contracts/Exception/RedirectionExceptionInterface.php',
    'Symfony\\Contracts\\HttpClient\\Exception\\ServerExceptionInterface' => $vendorDir . '/symfony/http-client-contracts/Exception/ServerExceptionInterface.php',
    'Symfony\\Contracts\\HttpClient\\Exception\\TimeoutExceptionInterface' => $vendorDir . '/symfony/http-client-contracts/Exception/TimeoutExceptionInterface.php',
    'Symfony\\Contracts\\HttpClient\\Exception\\TransportExceptionInterface' => $vendorDir . '/symfony/http-client-contracts/Exception/TransportExceptionInterface.php',
    'Symfony\\Contracts\\HttpClient\\HttpClientInterface' => $vendorDir . '/symfony/http-client-contracts/HttpClientInterface.php',
    'Symfony\\Contracts\\HttpClient\\ResponseInterface' => $vendorDir . '/symfony/http-client-contracts/ResponseInterface.php',
    'Symfony\\Contracts\\HttpClient\\ResponseStreamInterface' => $vendorDir . '/symfony/http-client-contracts/ResponseStreamInterface.php',
    'Symfony\\Contracts\\Service\\Attribute\\Required' => $vendorDir . '/symfony/service-contracts/Attribute/Required.php',
    'Symfony\\Contracts\\Service\\Attribute\\SubscribedService' => $vendorDir . '/symfony/service-contracts/Attribute/SubscribedService.php',
    'Symfony\\Contracts\\Service\\ResetInterface' => $vendorDir . '/symfony/service-contracts/ResetInterface.php',
    'Symfony\\Contracts\\Service\\ServiceCollectionInterface' => $vendorDir . '/symfony/service-contracts/ServiceCollectionInterface.php',
    'Symfony\\Contracts\\Service\\ServiceLocatorTrait' => $vendorDir . '/symfony/service-contracts/ServiceLocatorTrait.php',
    'Symfony\\Contracts\\Service\\ServiceMethodsSubscriberTrait' => $vendorDir . '/symfony/service-contracts/ServiceMethodsSubscriberTrait.php',
    'Symfony\\Contracts\\Service\\ServiceProviderInterface' => $vendorDir . '/symfony/service-contracts/ServiceProviderInterface.php',
    'Symfony\\Contracts\\Service\\ServiceSubscriberInterface' => $vendorDir . '/symfony/service-contracts/ServiceSubscriberInterface.php',
    'Symfony\\Contracts\\Service\\ServiceSubscriberTrait' => $vendorDir . '/symfony/service-contracts/ServiceSubscriberTrait.php',
    'Symfony\\Polyfill\\Ctype\\Ctype' => $vendorDir . '/symfony/polyfill-ctype/Ctype.php',
    'Symfony\\Polyfill\\Intl\\Grapheme\\Grapheme' => $vendorDir . '/symfony/polyfill-intl-grapheme/Grapheme.php',
    'Symfony\\Polyfill\\Intl\\Normalizer\\Normalizer' => $vendorDir . '/symfony/polyfill-intl-normalizer/Normalizer.php',
    'Symfony\\Polyfill\\Mbstring\\Mbstring' => $vendorDir . '/symfony/polyfill-mbstring/Mbstring.php',
);
