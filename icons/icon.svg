<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background with rounded corners -->
  <rect width="512" height="512" rx="51.2" ry="51.2" fill="url(#bgGradient)"/>
  
  <!-- Notification Bell Icon -->
  <g transform="translate(256,256)">
    <!-- Bell body -->
    <path d="M-60,-80 C-80,-80 -100,-60 -100,-40 L-100,20 C-100,40 -80,60 -60,60 L60,60 C80,60 100,40 100,20 L100,-40 C100,-60 80,-80 60,-80 L60,-100 C60,-120 40,-140 0,-140 C-40,-140 -60,-120 -60,-100 Z" fill="white"/>
    
    <!-- <PERSON> clapper -->
    <circle cx="0" cy="80" r="20" fill="white"/>
    
    <!-- Notification dot -->
    <circle cx="70" cy="-70" r="25" fill="#FF4444"/>
    
    <!-- Sound waves -->
    <path d="M120,-20 Q140,-40 120,-60" stroke="white" stroke-width="8" fill="none" opacity="0.7"/>
    <path d="M140,-10 Q170,-40 140,-70" stroke="white" stroke-width="8" fill="none" opacity="0.5"/>
    
    <path d="M-120,-20 Q-140,-40 -120,-60" stroke="white" stroke-width="8" fill="none" opacity="0.7"/>
    <path d="M-140,-10 Q-170,-40 -140,-70" stroke="white" stroke-width="8" fill="none" opacity="0.5"/>
  </g>
</svg>
