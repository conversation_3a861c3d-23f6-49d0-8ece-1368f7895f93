# HTTPS Setup Guide for PWA

## Why HTTPS is Required

Your PWA needs HTTPS to enable:
- 📱 **PWA Installation** - Browsers require HTTPS for installable PWAs
- 🔔 **Push Notifications** - Service workers and push notifications require HTTPS
- 📷 **Camera Access** - QR scanner and image capture need HTTPS
- 📍 **Location Services** - Geolocation API requires HTTPS

## Quick HTTPS Setup Options

### Option 1: Using ngrok (Recommended for Development)

1. **Install ngrok** (if not already installed):
   ```bash
   # macOS
   brew install ngrok
   
   # Or download from https://ngrok.com/download
   ```

2. **Start your PHP server** (if not running):
   ```bash
   ./start-server.sh 8080
   ```

3. **Create HTTPS tunnel**:
   ```bash
   ngrok http 8080
   ```

4. **Use the HTTPS URL** provided by ngrok (e.g., `https://abc123.ngrok.io`)

### Option 2: Using Local SSL Certificate

1. **Generate SSL certificate**:
   ```bash
   openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes
   ```

2. **Start PHP with SSL**:
   ```bash
   php -S localhost:8443 -t . cert.pem
   ```

### Option 3: Using Docker with SSL

1. **Create docker-compose.yml**:
   ```yaml
   version: '3'
   services:
     web:
       image: php:8.1-apache
       ports:
         - "443:443"
       volumes:
         - .:/var/www/html
       environment:
         - APACHE_DOCUMENT_ROOT=/var/www/html
   ```

### Option 4: Deploy to HTTPS Hosting

Deploy to any of these services that provide HTTPS by default:
- **Netlify** (free)
- **Vercel** (free)
- **GitHub Pages** (free)
- **Firebase Hosting** (free)
- **Heroku** (free tier available)

## Testing Push Notifications

Once you have HTTPS set up:

1. **Open your app** in the HTTPS URL
2. **Subscribe to notifications** - Click "Subscribe to Notifications"
3. **Allow permissions** when prompted
4. **Send test notification** - Use the "Test Push Notification" button
5. **Check browser console** for any errors

## Current Status

✅ **Working Features:**
- Subscription management (saving to JSON files)
- Push notification payload creation
- Service worker registration
- PWA manifest

⚠️ **Needs HTTPS:**
- PWA installation
- Actual push notification delivery
- Camera access for QR scanner
- Location services

## Troubleshooting

### Push Notifications Not Arriving

1. **Check HTTPS** - Must use HTTPS, not HTTP
2. **Check browser console** for errors
3. **Verify VAPID keys** are correctly set
4. **Check notification permissions** are granted
5. **Test in different browsers** (Chrome, Firefox, Edge)

### PWA Not Installable

1. **Use HTTPS** - Required for installation
2. **Check manifest.json** - Must be valid
3. **Verify service worker** - Must register successfully
4. **Check icons** - All icon sizes must be available

### QR Scanner Not Working

1. **Use HTTPS** - Camera requires secure context
2. **Grant camera permissions**
3. **Check if jsQR library loads**
4. **Test on mobile device**

## Production Deployment

For production, consider:

1. **Real SSL certificate** from Let's Encrypt or certificate authority
2. **CDN** for better performance
3. **Database** instead of JSON files for subscriptions
4. **Rate limiting** for API endpoints
5. **Error monitoring** and logging
6. **Backup strategy** for subscription data

## Next Steps

1. Set up HTTPS using one of the options above
2. Test all PWA features
3. Customize the app design and functionality
4. Deploy to production hosting
5. Monitor and maintain the application

## Support

If you encounter issues:
- Check browser developer tools console
- Verify all files are accessible via HTTPS
- Test in incognito/private browsing mode
- Try different browsers and devices
